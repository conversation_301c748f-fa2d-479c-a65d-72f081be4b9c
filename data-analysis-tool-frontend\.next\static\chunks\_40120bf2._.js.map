{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/lib/encodeDecode.ts"], "sourcesContent": ["import Hashids from \"hashids\";\r\n\r\nconst salt = process.env.SALT || \"rushan-salt\";\r\n\r\nconst hashids = new Hashids(salt, 12);\r\n\r\nconst encode = (id: number) => {\r\n  return hashids.encode(id);\r\n};\r\n\r\nconst decode = (hash: string) => {\r\n  const decodedNumberLike = hashids.decode(hash)[0];\r\n  const decoded =\r\n    typeof decodedNumberLike === \"bigint\"\r\n      ? decodedNumberLike < Number.MAX_SAFE_INTEGER\r\n        ? Number(decodedNumberLike)\r\n        : null\r\n      : typeof decodedNumberLike === \"number\"\r\n      ? decodedNumberLike\r\n      : null;\r\n  return decoded;\r\n};\r\n\r\nexport { encode, decode };\r\n"], "names": [], "mappings": ";;;;AAEa;AAFb;;AAEA,MAAM,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,IAAI,IAAI;AAEjC,MAAM,UAAU,IAAI,4IAAA,CAAA,UAAO,CAAC,MAAM;AAElC,MAAM,SAAS,CAAC;IACd,OAAO,QAAQ,MAAM,CAAC;AACxB;AAEA,MAAM,SAAS,CAAC;IACd,MAAM,oBAAoB,QAAQ,MAAM,CAAC,KAAK,CAAC,EAAE;IACjD,MAAM,UACJ,OAAO,sBAAsB,WACzB,oBAAoB,OAAO,gBAAgB,GACzC,OAAO,qBACP,OACF,OAAO,sBAAsB,WAC7B,oBACA;IACN,OAAO;AACT", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/lib/axios.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nconst axiosInstance = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000/api\",\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n  withCredentials: true,\r\n});\r\n\r\n// Add request interceptor to handle auth token\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    // You can add auth token here if needed\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add response interceptor to handle errors\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.code === \"ERR_NETWORK\") {\r\n      console.error(\r\n        \"Network error - Please check if the backend server is running\"\r\n      );\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAGW;AAHX;;AAEA,MAAM,gBAAgB,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACjC,SAAS,iEAAmC;IAC5C,SAAS;QACP,gBAAgB;IAClB;IACA,iBAAiB;AACnB;AAEA,+CAA+C;AAC/C,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;IACC,wCAAwC;IACxC,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,4CAA4C;AAC5C,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,IAAI,KAAK,eAAe;QAChC,QAAQ,KAAK,CACX;IAEJ;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/lib/api/form-builder.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\nimport { ContextType } from \"@/types\";\r\n\r\nconst getQuestionsEndPoint = (contextType: ContextType) => {\r\n  if (contextType === \"project\") return \"/questions\";\r\n  else if (contextType === \"template\") return \"/template-questions\";\r\n  else if (contextType === \"questionBlock\") return \"/question-blocks\";\r\n  throw new Error(\"Unsupported context type\");\r\n};\r\n\r\nconst fetchQuestions = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/questions/${projectId}`);\r\n  return data.questions;\r\n};\r\n\r\nconst fetchTemplateQuestions = async ({\r\n  templateId,\r\n}: {\r\n  templateId: number;\r\n}) => {\r\n  const { data } = await axios.get(`/template-questions/${templateId}`);\r\n  return data.questions;\r\n};\r\n\r\nconst addQuestion = async ({\r\n  contextType,\r\n  contextId,\r\n  dataToSend,\r\n  position,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint?: string;\r\n    placeholder?: string;\r\n    inputType: string;\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n    }[];\r\n  };\r\n  position?: number;\r\n}) => {\r\n  // For question blocks, we don't need to include the contextId in the URL\r\n  // The userId is taken from the authenticated user in the backend\r\n  const url =\r\n    contextType === \"questionBlock\"\r\n      ? `${getQuestionsEndPoint(contextType)}`\r\n      : `${getQuestionsEndPoint(contextType)}/${contextId}`;\r\n\r\n  const { data } = await axios.post(url, {\r\n    ...dataToSend,\r\n    position: position || 1,\r\n  });\r\n  return data;\r\n};\r\n\r\nconst deleteQuestion = async ({\r\n  contextType,\r\n  id,\r\n  projectId,\r\n}: {\r\n  contextType: ContextType;\r\n  id: number;\r\n  projectId: number;\r\n}) => {\r\n  const { data } = await axios.delete(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${projectId}`\r\n  );\r\n  return data;\r\n};\r\n\r\nconst duplicateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  contextId: number;\r\n}) => {\r\n  // For question blocks, we don't need to send the contextId in the body\r\n  // The userId is taken from the authenticated user in the backend\r\n  const requestBody =\r\n    contextType === \"questionBlock\"\r\n      ? {}\r\n      : contextType === \"project\"\r\n        ? { projectId: contextId }\r\n        : { templateId: contextId };\r\n\r\n  const { data } = await axios.post(\r\n    `${getQuestionsEndPoint(\r\n      contextType\r\n    )}/duplicate/${id}?projectId=${contextId}`,\r\n    requestBody\r\n  );\r\n\r\n  return data;\r\n};\r\n\r\nconst updateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  dataToSend,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint: string;\r\n    placeholder: string;\r\n    position?: number; // Optional position field to preserve question order\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n    }[];\r\n  };\r\n  contextId: number;\r\n}) => {\r\n  const { data } = await axios.patch(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${contextId}`,\r\n    dataToSend\r\n  );\r\n  return data;\r\n};\r\n\r\nconst fetchQuestionBlockQuestions = async () => {\r\n  try {\r\n    const response = await axios.get(`/question-blocks`);\r\n    return response.data.questions || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching question block questions:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nconst updateQuestionPositions = async ({\r\n  contextType,\r\n  contextId,\r\n  questionPositions,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  questionPositions: { id: number; position: number }[];\r\n}) => {\r\n  // Only support position updates for projects currently\r\n  if (contextType !== \"project\") {\r\n    throw new Error(\"Question position updates are only supported for projects\");\r\n  }\r\n\r\n  const url = `${getQuestionsEndPoint(contextType)}/positions?projectId=${contextId}`;\r\n  const payload = { questionPositions };\r\n\r\n  try {\r\n    const { data } = await axios.patch(url, payload);\r\n    return data;\r\n  } catch (error: any) {\r\n    console.error(\"Update failed - Full error:\", error);\r\n    console.error(\"Update failed - Error details:\", {\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      data: error.response?.data,\r\n      message: error.message,\r\n      config: {\r\n        url: error.config?.url,\r\n        method: error.config?.method,\r\n        data: error.config?.data,\r\n      },\r\n    });\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport {\r\n  fetchQuestions,\r\n  fetchTemplateQuestions,\r\n  addQuestion,\r\n  deleteQuestion,\r\n  duplicateQuestion,\r\n  updateQuestion,\r\n  fetchQuestionBlockQuestions,\r\n  updateQuestionPositions,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGA,MAAM,uBAAuB,CAAC;IAC5B,IAAI,gBAAgB,WAAW,OAAO;SACjC,IAAI,gBAAgB,YAAY,OAAO;SACvC,IAAI,gBAAgB,iBAAiB,OAAO;IACjD,MAAM,IAAI,MAAM;AAClB;AAEA,MAAM,iBAAiB,OAAO,EAAE,SAAS,EAAyB;IAChE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW;IAC1D,OAAO,KAAK,SAAS;AACvB;AAEA,MAAM,yBAAyB,OAAO,EACpC,UAAU,EAGX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY;IACpE,OAAO,KAAK,SAAS;AACvB;AAEA,MAAM,cAAc,OAAO,EACzB,WAAW,EACX,SAAS,EACT,UAAU,EACV,QAAQ,EAiBT;IACC,yEAAyE;IACzE,iEAAiE;IACjE,MAAM,MACJ,gBAAgB,kBACZ,GAAG,qBAAqB,cAAc,GACtC,GAAG,qBAAqB,aAAa,CAAC,EAAE,WAAW;IAEzD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK;QACrC,GAAG,UAAU;QACb,UAAU,YAAY;IACxB;IACA,OAAO;AACT;AAEA,MAAM,iBAAiB,OAAO,EAC5B,WAAW,EACX,EAAE,EACF,SAAS,EAKV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CACjC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW;IAErE,OAAO;AACT;AAEA,MAAM,oBAAoB,OAAO,EAC/B,EAAE,EACF,WAAW,EACX,SAAS,EAKV;IACC,uEAAuE;IACvE,iEAAiE;IACjE,MAAM,cACJ,gBAAgB,kBACZ,CAAC,IACD,gBAAgB,YACd;QAAE,WAAW;IAAU,IACvB;QAAE,YAAY;IAAU;IAEhC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,GAAG,qBACD,aACA,WAAW,EAAE,GAAG,WAAW,EAAE,WAAW,EAC1C;IAGF,OAAO;AACT;AAEA,MAAM,iBAAiB,OAAO,EAC5B,EAAE,EACF,WAAW,EACX,UAAU,EACV,SAAS,EAiBV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAChC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW,EACnE;IAEF,OAAO;AACT;AAEA,MAAM,8BAA8B;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC;QACnD,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,EAAE;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM;IACR;AACF;AAEA,MAAM,0BAA0B,OAAO,EACrC,WAAW,EACX,SAAS,EACT,iBAAiB,EAKlB;IACC,uDAAuD;IACvD,IAAI,gBAAgB,WAAW;QAC7B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,MAAM,GAAG,qBAAqB,aAAa,qBAAqB,EAAE,WAAW;IACnF,MAAM,UAAU;QAAE;IAAkB;IAEpC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,KAAK;QACxC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,QAAQ,KAAK,CAAC,kCAAkC;YAC9C,QAAQ,MAAM,QAAQ,EAAE;YACxB,YAAY,MAAM,QAAQ,EAAE;YAC5B,MAAM,MAAM,QAAQ,EAAE;YACtB,SAAS,MAAM,OAAO;YACtB,QAAQ;gBACN,KAAK,MAAM,MAAM,EAAE;gBACnB,QAAQ,MAAM,MAAM,EAAE;gBACtB,MAAM,MAAM,MAAM,EAAE;YACtB;QACF;QACA,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/lib/api/question-groups.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\n\r\n/**\r\n * Fetch all question groups for a project\r\n */\r\nexport const fetchQuestionGroups = async ({ projectId }: { projectId: number }) => {\r\n  try {\r\n    // Use the project endpoint to fetch the project with its question groups\r\n    const { data } = await axios.get(`/projects/form/${projectId}`);\r\n    console.log(\"Fetched project with question groups:\", data);\r\n\r\n    // Extract question groups from the project data\r\n    const questionGroups = data.data?.project?.questionGroup || [];\r\n    console.log(\"Extracted question groups:\", questionGroups);\r\n\r\n    return questionGroups;\r\n  } catch (error) {\r\n    console.error(\"Error fetching question groups from project endpoint:\", error);\r\n\r\n    // Fallback to direct question groups endpoint\r\n    try {\r\n      console.log(\"Trying fallback to direct question groups endpoint\");\r\n      const { data } = await axios.post(`/question-groups`, { projectId });\r\n      console.log(\"Fallback response:\", data);\r\n      return data.data?.projectGroup || [];\r\n    } catch (fallbackError) {\r\n      console.error(\"Error in fallback fetch:\", fallbackError);\r\n\r\n      // Last resort: create a dummy group for debugging\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.log(\"Creating dummy group for debugging\");\r\n        return [];\r\n      }\r\n\r\n      return [];\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Create a new question group\r\n */\r\nexport const createQuestionGroup = async ({\r\n  title,\r\n  order,\r\n  projectId,\r\n  selectedQuestionIds,\r\n}: {\r\n  title: string;\r\n  order: number;\r\n  projectId: number;\r\n  selectedQuestionIds?: number[];\r\n}) => {\r\n  try {\r\n    console.log(\"Creating question group with data:\", {\r\n      title,\r\n      order,\r\n      projectId,\r\n      selectedQuestionIds\r\n    });\r\n\r\n    const { data } = await axios.post(`/question-groups`, {\r\n      title,\r\n      order,\r\n      projectId,\r\n      selectedQuestionIds: selectedQuestionIds || [],\r\n    });\r\n\r\n    console.log(\"Create question group response:\", data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error creating question group:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Update an existing question group\r\n */\r\nexport const updateQuestionGroup = async ({\r\n  id,\r\n  title,\r\n  order,\r\n  selectedQuestionIds,\r\n}: {\r\n  id: number;\r\n  title: string;\r\n  order: number;\r\n  selectedQuestionIds?: number[];\r\n}) => {\r\n  try {\r\n    console.log(\"Updating question group with data:\", {\r\n      id,\r\n      title,\r\n      order,\r\n      selectedQuestionIds\r\n    });\r\n\r\n    const { data } = await axios.patch(`/question-groups`, {\r\n      id,\r\n      title,\r\n      order,\r\n      selectedQuestionIds,\r\n    });\r\n\r\n    console.log(\"Update question group response:\", data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating question group:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Delete a question group\r\n */\r\nexport const deleteQuestionGroup = async ({ id }: { id: number }) => {\r\n  try {\r\n    console.log(`Deleting question group with ID: ${id}`);\r\n    const { data } = await axios.delete(`/question-groups/${id}`);\r\n    console.log(\"Delete question group response:\", data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting question group:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Delete a question group and all its questions\r\n */\r\nexport const deleteQuestionAndGroup = async ({ id }: { id: number }) => {\r\n  try {\r\n    console.log(`Deleting question group and its questions with ID: ${id}`);\r\n    const { data } = await axios.delete(`/question-groups/group/question/${id}`);\r\n    console.log(\"Delete question group and questions response:\", data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting question group and questions:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Remove a question from a group\r\n */\r\nexport const removeQuestionFromGroup = async ({\r\n  groupId,\r\n  questionId,\r\n}: {\r\n  groupId: number;\r\n  questionId: number;\r\n}) => {\r\n  const { data } = await axios.patch(`/question-groups/question/remove`, {\r\n    groupId,\r\n    questionId,\r\n  });\r\n  return data;\r\n};\r\n\r\n/**\r\n * Move a question from one group to another\r\n */\r\nexport const moveQuestionBetweenGroups = async ({\r\n  groupId,\r\n  newGroupId,\r\n  questionId,\r\n}: {\r\n  groupId: number;\r\n  newGroupId: number;\r\n  questionId: number;\r\n}) => {\r\n  const { data } = await axios.patch(`/question-groups/question/move`, {\r\n    groupId,\r\n    newGroupId,\r\n    questionId,\r\n  });\r\n  return data;\r\n};\r\n\r\n// Parent group functionality removed for simplicity\r\n"], "names": [], "mappings": ";;;;;;;;;AA6BU;AA7BV;;AAKO,MAAM,sBAAsB,OAAO,EAAE,SAAS,EAAyB;IAC5E,IAAI;QACF,yEAAyE;QACzE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;QAC9D,QAAQ,GAAG,CAAC,yCAAyC;QAErD,gDAAgD;QAChD,MAAM,iBAAiB,KAAK,IAAI,EAAE,SAAS,iBAAiB,EAAE;QAC9D,QAAQ,GAAG,CAAC,8BAA8B;QAE1C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yDAAyD;QAEvE,8CAA8C;QAC9C,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE;gBAAE;YAAU;YAClE,QAAQ,GAAG,CAAC,sBAAsB;YAClC,OAAO,KAAK,IAAI,EAAE,gBAAgB,EAAE;QACtC,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,kDAAkD;YAClD,wCAA4C;gBAC1C,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;;QAGF;IACF;AACF;AAKO,MAAM,sBAAsB,OAAO,EACxC,KAAK,EACL,KAAK,EACL,SAAS,EACT,mBAAmB,EAMpB;IACC,IAAI;QACF,QAAQ,GAAG,CAAC,sCAAsC;YAChD;YACA;YACA;YACA;QACF;QAEA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE;YACpD;YACA;YACA;YACA,qBAAqB,uBAAuB,EAAE;QAChD;QAEA,QAAQ,GAAG,CAAC,mCAAmC;QAC/C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,MAAM,sBAAsB,OAAO,EACxC,EAAE,EACF,KAAK,EACL,KAAK,EACL,mBAAmB,EAMpB;IACC,IAAI;QACF,QAAQ,GAAG,CAAC,sCAAsC;YAChD;YACA;YACA;YACA;QACF;QAEA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,EAAE;YACrD;YACA;YACA;YACA;QACF;QAEA,QAAQ,GAAG,CAAC,mCAAmC;QAC/C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,MAAM,sBAAsB,OAAO,EAAE,EAAE,EAAkB;IAC9D,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,IAAI;QACpD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,IAAI;QAC5D,QAAQ,GAAG,CAAC,mCAAmC;QAC/C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,MAAM,yBAAyB,OAAO,EAAE,EAAE,EAAkB;IACjE,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,IAAI;QACtE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,gCAAgC,EAAE,IAAI;QAC3E,QAAQ,GAAG,CAAC,iDAAiD;QAC7D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM;IACR;AACF;AAKO,MAAM,0BAA0B,OAAO,EAC5C,OAAO,EACP,UAAU,EAIX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAE;QACrE;QACA;IACF;IACA,OAAO;AACT;AAKO,MAAM,4BAA4B,OAAO,EAC9C,OAAO,EACP,UAAU,EACV,UAAU,EAKX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAE;QACnE;QACA;QACA;IACF;IACA,OAAO;AACT,GAEA,oDAAoD", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/lib/api/projects.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\nimport { Project } from \"@/types\";\r\n\r\nconst fetchProjectById = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/projects/${projectId}`);\r\n  return data.project;\r\n};\r\n\r\nconst createProjectFromTemplate = async (dataToSend: {\r\n  templateId: number;\r\n  name: string;\r\n  description: string;\r\n  sector: string;\r\n  country: string;\r\n}) => {\r\n  const { data } = await axios.post(`/projects/from-template`, dataToSend);\r\n  return data;\r\n};\r\n\r\n//Fetch all projects for the current user\r\nconst fetchProjects = async (): Promise<Project[]> => {\r\n  try {\r\n    const { data } = await axios.get(`/projects`);\r\n    return data.projects;\r\n  } catch (error) {\r\n    console.error(\"Error fetching projects:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Delete project\r\nconst deleteProject = async (projectId: number) => {\r\n  const { data } = await axios.delete(`/projects/delete/${projectId}`);\r\n  return data;\r\n};\r\n\r\n// Delete multiple projects\r\nconst deleteMultipleProjects = async (projectIds: number[]) => {\r\n  try {\r\n    const { data } = await axios.delete(`/projects/delete-multiple`, {\r\n      data: { projectIds },\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting multiple projects:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n//Archive project\r\nconst archiveProject = async (projectId: number) => {\r\n  try {\r\n    const { data } = await axios.patch(`/projects/change-status/${projectId}`, {\r\n      status: \"archived\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error archiving project:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n//Deploy project\r\nconst deployProject = async (\r\n  projectId: number,\r\n  isUnarchive: boolean = false\r\n) => {\r\n  try {\r\n    const { data } = await axios.patch(`/projects/change-status/${projectId}`, {\r\n      status: \"deployed\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deploying project:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Archive multiple projects\r\nconst archiveMultipleProjects = async (projectIds: number[]) => {\r\n  try {\r\n    const { data } = await axios.patch(`/projects/update-many-status`, {\r\n      projectIds,\r\n      status: \"archived\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error archiving multiple projects:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Check if user exists by email\r\nconst checkUserExists = async (email: string) => {\r\n  try {\r\n    const { data } = await axios.post(`/users/check-email`, { email });\r\n    return data;\r\n  } catch (error: any) {\r\n    // Format error message consistently\r\n    const errorMessage =\r\n      typeof error.response?.data?.message === \"object\"\r\n        ? JSON.stringify(error.response?.data?.message)\r\n        : error.response?.data?.message ||\r\n          error.message ||\r\n          \"Failed to check user\";\r\n\r\n    throw new Error(errorMessage);\r\n  }\r\n};\r\n\r\n// Add user to project by email\r\nconst addProjectUser = async ({\r\n  projectId,\r\n  email,\r\n  permissions,\r\n}: {\r\n  projectId: number;\r\n  email: string;\r\n  permissions: Record<string, boolean>;\r\n}) => {\r\n  try {\r\n    // First check if the user exists\r\n    const userData = await checkUserExists(email);\r\n\r\n    if (!userData || !userData.success) {\r\n      throw new Error(userData?.message || \"User not found\");\r\n    }\r\n\r\n    // Now use the user ID to add them to the project\r\n    const { data } = await axios.post(`/project-users`, {\r\n      userId: userData.user.id,\r\n      projectId,\r\n      permission: permissions,\r\n    });\r\n\r\n    return data;\r\n  } catch (error: any) {\r\n    console.error(\"Error adding user to project:\", error);\r\n    // Format error message as a string\r\n    const errorMessage =\r\n      typeof error.response?.data?.message === \"object\"\r\n        ? JSON.stringify(error.response?.data?.message)\r\n        : error.response?.data?.message ||\r\n          error.message ||\r\n          \"Failed to add user\";\r\n\r\n    throw new Error(errorMessage);\r\n  }\r\n};\r\n\r\n// Fetch all users for a specific project\r\nconst fetchProjectUsers = async (projectId: number) => {\r\n  try {\r\n    const { data } = await axios.get(`/project-users/${projectId}`);\r\n    return data.data.AllUser;\r\n  } catch (error: any) {\r\n    console.error(\"Error fetching project users:\", error);\r\n    const errorMessage =\r\n      typeof error.response?.data?.message === \"object\"\r\n        ? JSON.stringify(error.response?.data?.message)\r\n        : error.response?.data?.message ||\r\n          error.message ||\r\n          \"Failed to fetch project users\";\r\n\r\n    throw new Error(errorMessage);\r\n  }\r\n};\r\n\r\n// Create answer submission\r\nconst createAnswerSubmission = async (\r\n  answers: {\r\n    projectId: number;\r\n    questionId: number;\r\n    answerType: string;\r\n    value?: string | number | boolean;\r\n    imageUrl?: string;\r\n    questionOptionId?: number | number[];\r\n    isOtherOption?: boolean;\r\n  }[]\r\n) => {\r\n  try {\r\n    const { data } = await axios.post(`/answers/multiple`, answers);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error creating answer submission:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update answer submission\r\nconst updateAnswerSubmission = async (\r\n  projectId: number,\r\n  submissionId: number,\r\n  answers: {\r\n    projectId: number;\r\n    questionId: number;\r\n    answerType: string;\r\n    value?: string | number | boolean;\r\n    imageUrl?: string;\r\n    questionOptionId?: number | number[];\r\n    isOtherOption?: boolean;\r\n  }[]\r\n) => {\r\n  try {\r\n    const { data } = await axios.patch(`/form-submissions/${projectId}/${submissionId}`, { answers });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating answer submission:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n\r\n\r\n\r\nexport {\r\n  fetchProjectById,\r\n  fetchProjects,\r\n  deleteProject,\r\n  deleteMultipleProjects,\r\n  archiveMultipleProjects,\r\n  createProjectFromTemplate,\r\n  archiveProject,\r\n  deployProject,\r\n  addProjectUser,\r\n  checkUserExists,\r\n  fetchProjectUsers,\r\n  createAnswerSubmission,\r\n  updateAnswerSubmission,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAGA,MAAM,mBAAmB,OAAO,EAAE,SAAS,EAAyB;IAClE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW;IACzD,OAAO,KAAK,OAAO;AACrB;AAEA,MAAM,4BAA4B,OAAO;IAOvC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,EAAE;IAC7D,OAAO;AACT;AAEA,yCAAyC;AACzC,MAAM,gBAAgB;IACpB,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;QAC5C,OAAO,KAAK,QAAQ;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEA,iBAAiB;AACjB,MAAM,gBAAgB,OAAO;IAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,WAAW;IACnE,OAAO;AACT;AAEA,2BAA2B;AAC3B,MAAM,yBAAyB,OAAO;IACpC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,yBAAyB,CAAC,EAAE;YAC/D,MAAM;gBAAE;YAAW;QACrB;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAEA,iBAAiB;AACjB,MAAM,iBAAiB,OAAO;IAC5B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,wBAAwB,EAAE,WAAW,EAAE;YACzE,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEA,gBAAgB;AAChB,MAAM,gBAAgB,OACpB,WACA,cAAuB,KAAK;IAE5B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,wBAAwB,EAAE,WAAW,EAAE;YACzE,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEA,4BAA4B;AAC5B,MAAM,0BAA0B,OAAO;IACrC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;YACjE;YACA,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM;IACR;AACF;AAEA,gCAAgC;AAChC,MAAM,kBAAkB,OAAO;IAC7B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,EAAE;YAAE;QAAM;QAChE,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,oCAAoC;QACpC,MAAM,eACJ,OAAO,MAAM,QAAQ,EAAE,MAAM,YAAY,WACrC,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM,WACrC,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb;QAEN,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,+BAA+B;AAC/B,MAAM,iBAAiB,OAAO,EAC5B,SAAS,EACT,KAAK,EACL,WAAW,EAKZ;IACC,IAAI;QACF,iCAAiC;QACjC,MAAM,WAAW,MAAM,gBAAgB;QAEvC,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;YAClC,MAAM,IAAI,MAAM,UAAU,WAAW;QACvC;QAEA,iDAAiD;QACjD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE;YAClD,QAAQ,SAAS,IAAI,CAAC,EAAE;YACxB;YACA,YAAY;QACd;QAEA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,mCAAmC;QACnC,MAAM,eACJ,OAAO,MAAM,QAAQ,EAAE,MAAM,YAAY,WACrC,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM,WACrC,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb;QAEN,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,yCAAyC;AACzC,MAAM,oBAAoB,OAAO;IAC/B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;QAC9D,OAAO,KAAK,IAAI,CAAC,OAAO;IAC1B,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,eACJ,OAAO,MAAM,QAAQ,EAAE,MAAM,YAAY,WACrC,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM,WACrC,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb;QAEN,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,2BAA2B;AAC3B,MAAM,yBAAyB,OAC7B;IAUA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,EAAE;QACvD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAEA,2BAA2B;AAC3B,MAAM,yBAAyB,OAC7B,WACA,cACA;IAUA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAAE,UAAU,CAAC,EAAE,cAAc,EAAE;YAAE;QAAQ;QAC/F,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/general/Spinner.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst Spinner = () => {\r\n  return (\r\n    <div className=\"w-full flex items-center justify-center\">\r\n      <div className=\"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16\"></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Spinner;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,UAAU;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;KANM;uCAQS", "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Format a date into a readable string format\r\n * @param date - Date object or string to format\r\n * @param format - Optional format type ('short', 'long', or 'full')\r\n * @returns Formatted date string\r\n */\r\nexport function formatDate(\r\n  date: Date | string,\r\n  format: \"short\" | \"long\" | \"full\" = \"short\"\r\n): string {\r\n  if (!date) return \"\";\r\n\r\n  try {\r\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\r\n\r\n    // Return empty string if invalid date\r\n    if (isNaN(dateObj.getTime())) return \"\";\r\n\r\n    switch (format) {\r\n      case \"short\":\r\n        return dateObj.toLocaleDateString(undefined, {\r\n          year: \"numeric\",\r\n          month: \"short\",\r\n          day: \"numeric\",\r\n        });\r\n\r\n      case \"long\":\r\n        return dateObj.toLocaleDateString(undefined, {\r\n          year: \"numeric\",\r\n          month: \"long\",\r\n          day: \"numeric\",\r\n          hour: \"2-digit\",\r\n          minute: \"2-digit\",\r\n        });\r\n\r\n      case \"full\":\r\n        return dateObj.toLocaleDateString(undefined, {\r\n          year: \"numeric\",\r\n          month: \"long\",\r\n          day: \"numeric\",\r\n          weekday: \"long\",\r\n          hour: \"2-digit\",\r\n          minute: \"2-digit\",\r\n          second: \"2-digit\",\r\n        });\r\n\r\n      default:\r\n        return dateObj.toLocaleDateString();\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error formatting date:\", error);\r\n    return String(date);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,WACd,IAAmB,EACnB,SAAoC,OAAO;IAE3C,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;QAE5D,sCAAsC;QACtC,IAAI,MAAM,QAAQ,OAAO,KAAK,OAAO;QAErC,OAAQ;YACN,KAAK;gBACH,OAAO,QAAQ,kBAAkB,CAAC,WAAW;oBAC3C,MAAM;oBACN,OAAO;oBACP,KAAK;gBACP;YAEF,KAAK;gBACH,OAAO,QAAQ,kBAAkB,CAAC,WAAW;oBAC3C,MAAM;oBACN,OAAO;oBACP,KAAK;oBACL,MAAM;oBACN,QAAQ;gBACV;YAEF,KAAK;gBACH,OAAO,QAAQ,kBAAkB,CAAC,WAAW;oBAC3C,MAAM;oBACN,OAAO;oBACP,KAAK;oBACL,SAAS;oBACT,MAAM;oBACN,QAAQ;oBACR,QAAQ;gBACV;YAEF;gBACE,OAAO,QAAQ,kBAAkB;QACrC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,OAAO;IAChB;AACF", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label } "], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport interface TextareaProps\r\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\r\n  ({ className, ...props }, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          \"flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nTextarea.displayName = \"Textarea\";\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mUACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { CheckIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Checkbox({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\r\n  return (\r\n    <CheckboxPrimitive.Root\r\n      data-slot=\"checkbox\"\r\n      className={cn(\r\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <CheckboxPrimitive.Indicator\r\n        data-slot=\"checkbox-indicator\"\r\n        className=\"flex items-center justify-center text-current transition-none\"\r\n      >\r\n        <CheckIcon className=\"size-3.5\" />\r\n      </CheckboxPrimitive.Indicator>\r\n    </CheckboxPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\r\nimport { Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Root\r\n      className={cn(\"grid gap-2\", className)}\r\n      {...props}\r\n      ref={ref}\r\n    />\r\n  )\r\n})\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  )\r\n})\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\r\n\r\nexport { RadioGroup, RadioGroupItem } "], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;;AACA,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yOACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;;AACA,eAAe,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/ui/table.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gHACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]\",\r\n        \"focus-visible:outline-none\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mcACA,qFACA,8BACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAfS", "debugId": null}}, {"offset": {"line": 927, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/lib/api/table.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\n\r\nexport interface TableColumn {\r\n  id: number;\r\n  columnName: string;\r\n  parentColumnId?: number;\r\n  childColumns?: TableColumn[];\r\n}\r\n\r\nexport interface DefaultValue {\r\n  columnId: number;\r\n  value: string;\r\n  code?: string;\r\n}\r\n\r\nexport interface TableRow {\r\n  id: number;\r\n  rowsName: string;\r\n  defaultValues?: DefaultValue[];\r\n}\r\n\r\nexport interface CellValue {\r\n  columnId: number;\r\n  rowsId: number;\r\n  value: string;\r\n  code?: string;\r\n  isDefault?: boolean;\r\n}\r\n\r\nexport interface TableQuestion {\r\n  id: number;\r\n  label: string;\r\n  inputType: string;\r\n  tableColumns: TableColumn[];\r\n  tableRows: TableRow[];\r\n}\r\n\r\n// Fetch table structure (columns and rows)\r\nexport const fetchTableStructure = async (questionId: number) => {\r\n  try {\r\n    console.log(`Fetching table structure for questionId: ${questionId}`);\r\n\r\n    if (!questionId || isNaN(questionId)) {\r\n      console.error(\"Invalid questionId:\", questionId);\r\n      throw new Error(\"Invalid question ID provided\");\r\n    }\r\n\r\n    // First try the table-questions endpoint\r\n    try {\r\n      const response = await axios.get(`/table-questions/${questionId}`);\r\n      console.log(\"Response from /table-questions/:\", response.data);\r\n\r\n      // Check if the response has the expected structure\r\n      if (response.data && response.data.data && response.data.data.question) {\r\n        console.log(\"Using question from data.data.question\");\r\n        // Include both question and cellValues in the response\r\n        const tableData = response.data.data.question;\r\n        const cellValues = response.data.data.cellValues || {};\r\n\r\n        // Merge cellValues into the table data for backward compatibility\r\n        return {\r\n          ...tableData,\r\n          cellValues: cellValues,\r\n        };\r\n      } else if (response.data && response.data.data) {\r\n        console.log(\"Using data from data.data\");\r\n        return response.data.data;\r\n      } else if (response.data && response.data.success) {\r\n        console.log(\"Using data from response.data\");\r\n        return response.data;\r\n      }\r\n    } catch (err) {\r\n      console.log(\"Error from /table-questions/ endpoint:\", err);\r\n      // Continue to try the next endpoint\r\n    }\r\n\r\n    // If that fails, try the questions endpoint\r\n    try {\r\n      const response = await axios.get(`/questions/${questionId}`);\r\n      console.log(\"Response from /questions/:\", response.data);\r\n\r\n      if (response.data && response.data.data) {\r\n        console.log(\"Using data from questions endpoint\");\r\n        return response.data.data;\r\n      }\r\n    } catch (err) {\r\n      console.log(\"Error from /questions/ endpoint:\", err);\r\n      // Continue to try the next endpoint\r\n    }\r\n\r\n    // If that fails, try the tables endpoint as a last resort\r\n    try {\r\n      const response = await axios.get(`/tables/${questionId}`);\r\n      console.log(\"Response from /tables/:\", response.data);\r\n\r\n      if (response.data && response.data.data && response.data.data.question) {\r\n        console.log(\"Using question from tables endpoint\");\r\n        return response.data.data.question;\r\n      }\r\n    } catch (err) {\r\n      console.log(\"Error from /tables/ endpoint:\", err);\r\n    }\r\n\r\n    // If all endpoints fail, throw an error\r\n    console.error(\"All endpoints failed to return valid data\");\r\n    throw new Error(\"Failed to fetch table structure from any endpoint\");\r\n  } catch (error) {\r\n    console.error(\"Error fetching table structure:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Save cell values\r\nexport const saveCellValues = async (\r\n  questionId: number,\r\n  cellValues: CellValue[]\r\n) => {\r\n  try {\r\n    const { data } = await axios.post(`/table-questions/cells`, {\r\n      questionId,\r\n      cellValues,\r\n    });\r\n    return data.data;\r\n  } catch (error) {\r\n    console.error(\"Error saving cell values:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Create a new table\r\n// IMPORTANT: When specifying parentColumnId for new columns, you need to use position-based indices\r\n// (1-based) that reference the position of the parent column in the array.\r\n// For example, if column B is a child of column A, and column A is the first column in the array,\r\n// then column B's parentColumnId should be 1.\r\n// This is different from updateTable, which uses actual database IDs.\r\nexport const createTable = async (\r\n  label: string,\r\n  projectId: number,\r\n  columns: { columnName: string; parentColumnId?: number }[],\r\n  rows?: { rowsName: string; defaultValues?: DefaultValue[] }[]\r\n) => {\r\n  try {\r\n    // Validate inputs before sending to API\r\n    if (!label || !label.trim()) {\r\n      throw new Error(\"Table label is required\");\r\n    }\r\n\r\n    if (!projectId || isNaN(projectId)) {\r\n      throw new Error(\"Valid project ID is required\");\r\n    }\r\n\r\n    if (!columns || !Array.isArray(columns) || columns.length === 0) {\r\n      throw new Error(\"At least one column is required\");\r\n    }\r\n\r\n    // Rows are now optional - validate only if provided\r\n    if (rows && !Array.isArray(rows)) {\r\n      throw new Error(\"Rows must be an array if provided\");\r\n    }\r\n\r\n    // Ensure all columns have valid names\r\n    const invalidColumns = columns.filter(\r\n      (col) => !col.columnName || !col.columnName.trim()\r\n    );\r\n    if (invalidColumns.length > 0) {\r\n      throw new Error(\"All columns must have valid names\");\r\n    }\r\n\r\n    // Ensure all rows have valid names if rows are provided\r\n    if (rows) {\r\n      const invalidRows = rows.filter(\r\n        (row) => !row.rowsName || !row.rowsName.trim()\r\n      );\r\n      if (invalidRows.length > 0) {\r\n        throw new Error(\"All rows must have valid names\");\r\n      }\r\n    }\r\n\r\n    // The columns are already ordered correctly with parent-child relationships\r\n    // We just need to pass them through to the backend\r\n    console.log(\"Columns for create:\", columns);\r\n\r\n    // Create a clean version of the columns to send to the backend\r\n    const cleanedColumns: {\r\n      columnName: string;\r\n      parentColumnId?: number;\r\n    }[] = columns.map((col) => ({\r\n      columnName: col.columnName,\r\n      parentColumnId: col.parentColumnId,\r\n    }));\r\n\r\n    // Log the columns being sent to the backend\r\n    console.log(\"Cleaned columns for backend:\", cleanedColumns);\r\n\r\n    // Log the rearranged columns\r\n    console.log(\"Rearranged columns for backend:\", cleanedColumns);\r\n\r\n    // Ensure rows is always an array, even if empty\r\n    const processedRows = rows || [];\r\n\r\n    // Process rows to ensure default values are properly formatted\r\n    const cleanedRows = processedRows.map((row) => {\r\n      const cleanedRow: {\r\n        rowsName: string;\r\n        defaultValues: DefaultValue[];\r\n      } = {\r\n        rowsName: row.rowsName.trim(),\r\n        defaultValues: [],\r\n      };\r\n\r\n      // Process default values if they exist\r\n      if (row.defaultValues && row.defaultValues.length > 0) {\r\n        console.log(\r\n          `Processing ${row.defaultValues.length} default values for row ${row.rowsName}:`,\r\n          row.defaultValues\r\n        );\r\n\r\n        // Filter out invalid default values but keep all valid ones\r\n        cleanedRow.defaultValues = row.defaultValues\r\n          .filter((dv) => {\r\n            const isValid =\r\n              dv.columnId &&\r\n              typeof dv.columnId === \"number\" &&\r\n              dv.columnId > 0 &&\r\n              dv.value !== undefined &&\r\n              dv.value !== null &&\r\n              String(dv.value).trim() !== \"\";\r\n\r\n            if (!isValid) {\r\n              console.warn(`Filtering out invalid default value:`, dv);\r\n            }\r\n            return isValid;\r\n          })\r\n          .map((dv) => {\r\n            const mappedValue = {\r\n              columnId: dv.columnId,\r\n              value: String(dv.value).trim(),\r\n              code: dv.code || String(dv.value).trim(),\r\n            };\r\n            console.log(`Mapped default value:`, mappedValue);\r\n            return mappedValue;\r\n          });\r\n      }\r\n\r\n      console.log(\r\n        `Row ${row.rowsName} has ${cleanedRow.defaultValues.length} default values after cleaning:`,\r\n        cleanedRow.defaultValues\r\n      );\r\n      return cleanedRow;\r\n    });\r\n\r\n    console.log(\"Creating table with data:\", {\r\n      label,\r\n      projectId,\r\n      columns: cleanedColumns,\r\n      rows: cleanedRows,\r\n    });\r\n\r\n    // Log the exact structure of rows with default values for debugging\r\n    console.log(\"Rows with default values:\");\r\n    cleanedRows.forEach((row, index) => {\r\n      console.log(`Row ${index + 1} (${row.rowsName}):`);\r\n      console.log(`  Default values: ${row.defaultValues.length}`);\r\n      row.defaultValues.forEach((dv, i) => {\r\n        console.log(\r\n          `    ${i + 1}. columnId: ${dv.columnId}, value: \"${\r\n            dv.value\r\n          }\", code: \"${dv.code || dv.value}\"`\r\n        );\r\n      });\r\n    });\r\n\r\n    // Use the table-questions endpoint which creates both a question and table structure\r\n    // Note: The axios instance is configured with baseURL that includes /api, so we don't need to add it here\r\n    const { data } = await axios.post(`/table-questions`, {\r\n      label,\r\n      projectId,\r\n      columns: cleanedColumns,\r\n      rows: cleanedRows, // Use the cleaned rows\r\n    });\r\n\r\n    console.log(\"Table created successfully:\", data);\r\n\r\n    if (!data || !data.success) {\r\n      throw new Error(data?.message || \"Failed to create table\");\r\n    }\r\n\r\n    return data.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error creating table:\", error);\r\n\r\n    // Enhance error message with response details if available\r\n    if (error.response) {\r\n      console.error(\"Response status:\", error.response.status);\r\n      console.error(\"Response data:\", error.response.data);\r\n\r\n      // If we have a more specific error message from the server, use it\r\n      if (error.response.data && error.response.data.message) {\r\n        error.message = error.response.data.message;\r\n      }\r\n    }\r\n\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Delete a table\r\nexport const deleteTable = async (tableId: number) => {\r\n  try {\r\n    const { data } = await axios.delete(`/table-questions/${tableId}`);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting table:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update an existing table\r\n// IMPORTANT: When specifying parentColumnId for existing columns, use the actual database ID of the parent column.\r\n// For new columns (without an ID), use the position (1-based index) of the parent column in the array.\r\n// For example:\r\n// - If column B is a child of existing column A with ID 123, then column B's parentColumnId should be 123.\r\n// - If column B is a child of new column A at position 1 in the array, then column B's parentColumnId should be 1.\r\nexport const updateTable = async (\r\n  tableId: number,\r\n  label: string,\r\n  columns: { id?: number; columnName: string; parentColumnId?: number }[],\r\n  rows?: { id?: number; rowsName: string; defaultValues?: DefaultValue[] }[]\r\n) => {\r\n  try {\r\n    // Validate inputs before sending to API\r\n    if (!label || !label.trim()) {\r\n      throw new Error(\"Table label is required\");\r\n    }\r\n\r\n    if (!tableId || isNaN(tableId)) {\r\n      throw new Error(\"Valid table ID is required\");\r\n    }\r\n\r\n    if (!columns || !Array.isArray(columns) || columns.length === 0) {\r\n      throw new Error(\"At least one column is required\");\r\n    }\r\n\r\n    // Rows are now optional - validate only if provided\r\n    if (rows && !Array.isArray(rows)) {\r\n      throw new Error(\"Rows must be an array if provided\");\r\n    }\r\n\r\n    // Ensure all columns have valid names\r\n    const invalidColumns = columns.filter(\r\n      (col) => !col.columnName || !col.columnName.trim()\r\n    );\r\n    if (invalidColumns.length > 0) {\r\n      throw new Error(\"All columns must have valid names\");\r\n    }\r\n\r\n    // Ensure all rows have valid names if rows are provided\r\n    if (rows) {\r\n      const invalidRows = rows.filter(\r\n        (row) => !row.rowsName || !row.rowsName.trim()\r\n      );\r\n      if (invalidRows.length > 0) {\r\n        throw new Error(\"All rows must have valid names\");\r\n      }\r\n    }\r\n\r\n    // Validate parent-child relationships\r\n    // Check for circular references or invalid parent IDs\r\n    const columnIdMap = new Map();\r\n    const columnPositionMap = new Map();\r\n\r\n    // Map columns by ID and position\r\n    columns.forEach((col, index) => {\r\n      if (col.id) {\r\n        columnIdMap.set(col.id, col);\r\n      }\r\n      // Store 1-based position\r\n      columnPositionMap.set(index + 1, col);\r\n    });\r\n\r\n    // Check each column with a parent\r\n    for (const col of columns) {\r\n      if (col.parentColumnId) {\r\n        // Ensure parentColumnId is a positive number\r\n        if (col.parentColumnId <= 0) {\r\n          throw new Error(\r\n            `Invalid parent column ID: ${col.parentColumnId}. Must be a positive number.`\r\n          );\r\n        }\r\n\r\n        // Try to find parent by ID first\r\n        let parentCol = columns.find((c) => c.id === col.parentColumnId);\r\n\r\n        // If not found by ID, try to find by position (for new columns)\r\n        if (!parentCol && col.parentColumnId <= columns.length) {\r\n          parentCol = columnPositionMap.get(col.parentColumnId);\r\n          console.log(\r\n            `Found parent by position ${col.parentColumnId}: ${parentCol?.columnName}`\r\n          );\r\n        }\r\n\r\n        // If we still can't find the parent, it's an error\r\n        if (!parentCol) {\r\n          throw new Error(\r\n            `Parent column with ID/position ${col.parentColumnId} not found in the columns array.`\r\n          );\r\n        }\r\n\r\n        // Check for circular references\r\n        // If this column has a parent, and that parent also has a parent,\r\n        // it would create a 3rd level, which we don't support\r\n        if (parentCol.parentColumnId) {\r\n          throw new Error(\r\n            \"Cannot create more than 2 levels of nested columns (parent → child → grandchild)\"\r\n          );\r\n        }\r\n      }\r\n    }\r\n\r\n    // The columns are already ordered correctly with parent-child relationships\r\n    // We just need to pass them through to the backend\r\n    console.log(\"Columns for update:\", columns);\r\n\r\n    // Create a clean version of the columns to send to the backend\r\n    const cleanedColumns: {\r\n      id?: number;\r\n      columnName: string;\r\n      parentColumnId?: number;\r\n    }[] = columns.map((col) => {\r\n      const cleanCol: {\r\n        id?: number;\r\n        columnName: string;\r\n        parentColumnId?: number;\r\n      } = {\r\n        columnName: col.columnName.trim(),\r\n      };\r\n\r\n      if (col.id) {\r\n        cleanCol.id = col.id;\r\n      }\r\n\r\n      if (col.parentColumnId !== undefined) {\r\n        cleanCol.parentColumnId = col.parentColumnId;\r\n      }\r\n\r\n      return cleanCol;\r\n    });\r\n\r\n    // Log the columns being sent to the backend\r\n    console.log(\"Cleaned columns for backend:\", cleanedColumns);\r\n\r\n    console.log(\"Updating table with data:\", {\r\n      tableId,\r\n      label,\r\n      columns: cleanedColumns,\r\n      rows,\r\n    });\r\n\r\n    // Use the table-questions endpoint to update the table\r\n    try {\r\n      const { data } = await axios.patch(`/table-questions/${tableId}`, {\r\n        label: label.trim(),\r\n        columns: cleanedColumns,\r\n        rows: rows\r\n          ? rows.map((row) => ({\r\n              ...row,\r\n              rowsName: row.rowsName.trim(),\r\n            }))\r\n          : [],\r\n      });\r\n\r\n      console.log(\"Table updated successfully:\", data);\r\n\r\n      if (!data || !data.success) {\r\n        throw new Error(data?.message || \"Failed to update table\");\r\n      }\r\n\r\n      return data.data;\r\n    } catch (apiError: any) {\r\n      console.error(\"API error updating table:\", apiError);\r\n\r\n      // Enhance error message with response details if available\r\n      if (apiError.response) {\r\n        console.error(\"Response status:\", apiError.response.status);\r\n        console.error(\"Response data:\", apiError.response.data);\r\n\r\n        // If we have a more specific error message from the server, use it\r\n        if (apiError.response.data && apiError.response.data.message) {\r\n          throw new Error(apiError.response.data.message);\r\n        }\r\n      }\r\n\r\n      // If we don't have a specific error message, throw the original error\r\n      throw apiError;\r\n    }\r\n  } catch (error: any) {\r\n    console.error(\"Error updating table:\", error);\r\n\r\n    // Rethrow the error with a clear message\r\n    if (error.message) {\r\n      throw new Error(`Failed to update table: ${error.message}`);\r\n    } else {\r\n      throw new Error(\"Failed to update table due to an unknown error\");\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAsCO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,YAAY;QAEpE,IAAI,CAAC,cAAc,MAAM,aAAa;YACpC,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,MAAM;QAClB;QAEA,yCAAyC;QACzC,IAAI;YACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,YAAY;YACjE,QAAQ,GAAG,CAAC,oCAAoC,SAAS,IAAI;YAE7D,mDAAmD;YACnD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACtE,QAAQ,GAAG,CAAC;gBACZ,uDAAuD;gBACvD,MAAM,YAAY,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;gBAC7C,MAAM,aAAa,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC;gBAErD,kEAAkE;gBAClE,OAAO;oBACL,GAAG,SAAS;oBACZ,YAAY;gBACd;YACF,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC9C,QAAQ,GAAG,CAAC;gBACZ,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACjD,QAAQ,GAAG,CAAC;gBACZ,OAAO,SAAS,IAAI;YACtB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,GAAG,CAAC,0CAA0C;QACtD,oCAAoC;QACtC;QAEA,4CAA4C;QAC5C,IAAI;YACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,YAAY;YAC3D,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;YAEvD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBACvC,QAAQ,GAAG,CAAC;gBACZ,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,GAAG,CAAC,oCAAoC;QAChD,oCAAoC;QACtC;QAEA,0DAA0D;QAC1D,IAAI;YACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY;YACxD,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;YAEpD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACtE,QAAQ,GAAG,CAAC;gBACZ,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;YACpC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,GAAG,CAAC,iCAAiC;QAC/C;QAEA,wCAAwC;QACxC,QAAQ,KAAK,CAAC;QACd,MAAM,IAAI,MAAM;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB,OAC5B,YACA;IAEA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,sBAAsB,CAAC,EAAE;YAC1D;YACA;QACF;QACA,OAAO,KAAK,IAAI;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAQO,MAAM,cAAc,OACzB,OACA,WACA,SACA;IAEA,IAAI;QACF,wCAAwC;QACxC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,aAAa,MAAM,YAAY;YAClC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,KAAK,GAAG;YAC/D,MAAM,IAAI,MAAM;QAClB;QAEA,oDAAoD;QACpD,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI;QAElD,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,IAAI,MAAM;YACR,MAAM,cAAc,KAAK,MAAM,CAC7B,CAAC,MAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI;YAE9C,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,4EAA4E;QAC5E,mDAAmD;QACnD,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,+DAA+D;QAC/D,MAAM,iBAGA,QAAQ,GAAG,CAAC,CAAC,MAAQ,CAAC;gBAC1B,YAAY,IAAI,UAAU;gBAC1B,gBAAgB,IAAI,cAAc;YACpC,CAAC;QAED,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,6BAA6B;QAC7B,QAAQ,GAAG,CAAC,mCAAmC;QAE/C,gDAAgD;QAChD,MAAM,gBAAgB,QAAQ,EAAE;QAEhC,+DAA+D;QAC/D,MAAM,cAAc,cAAc,GAAG,CAAC,CAAC;YACrC,MAAM,aAGF;gBACF,UAAU,IAAI,QAAQ,CAAC,IAAI;gBAC3B,eAAe,EAAE;YACnB;YAEA,uCAAuC;YACvC,IAAI,IAAI,aAAa,IAAI,IAAI,aAAa,CAAC,MAAM,GAAG,GAAG;gBACrD,QAAQ,GAAG,CACT,CAAC,WAAW,EAAE,IAAI,aAAa,CAAC,MAAM,CAAC,wBAAwB,EAAE,IAAI,QAAQ,CAAC,CAAC,CAAC,EAChF,IAAI,aAAa;gBAGnB,4DAA4D;gBAC5D,WAAW,aAAa,GAAG,IAAI,aAAa,CACzC,MAAM,CAAC,CAAC;oBACP,MAAM,UACJ,GAAG,QAAQ,IACX,OAAO,GAAG,QAAQ,KAAK,YACvB,GAAG,QAAQ,GAAG,KACd,GAAG,KAAK,KAAK,aACb,GAAG,KAAK,KAAK,QACb,OAAO,GAAG,KAAK,EAAE,IAAI,OAAO;oBAE9B,IAAI,CAAC,SAAS;wBACZ,QAAQ,IAAI,CAAC,CAAC,oCAAoC,CAAC,EAAE;oBACvD;oBACA,OAAO;gBACT,GACC,GAAG,CAAC,CAAC;oBACJ,MAAM,cAAc;wBAClB,UAAU,GAAG,QAAQ;wBACrB,OAAO,OAAO,GAAG,KAAK,EAAE,IAAI;wBAC5B,MAAM,GAAG,IAAI,IAAI,OAAO,GAAG,KAAK,EAAE,IAAI;oBACxC;oBACA,QAAQ,GAAG,CAAC,CAAC,qBAAqB,CAAC,EAAE;oBACrC,OAAO;gBACT;YACJ;YAEA,QAAQ,GAAG,CACT,CAAC,IAAI,EAAE,IAAI,QAAQ,CAAC,KAAK,EAAE,WAAW,aAAa,CAAC,MAAM,CAAC,+BAA+B,CAAC,EAC3F,WAAW,aAAa;YAE1B,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,6BAA6B;YACvC;YACA;YACA,SAAS;YACT,MAAM;QACR;QAEA,oEAAoE;QACpE,QAAQ,GAAG,CAAC;QACZ,YAAY,OAAO,CAAC,CAAC,KAAK;YACxB,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,QAAQ,CAAC,EAAE,CAAC;YACjD,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,aAAa,CAAC,MAAM,EAAE;YAC3D,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI;gBAC7B,QAAQ,GAAG,CACT,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,QAAQ,CAAC,UAAU,EAC/C,GAAG,KAAK,CACT,UAAU,EAAE,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;YAEvC;QACF;QAEA,qFAAqF;QACrF,0GAA0G;QAC1G,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE;YACpD;YACA;YACA,SAAS;YACT,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,+BAA+B;QAE3C,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;YAC1B,MAAM,IAAI,MAAM,MAAM,WAAW;QACnC;QAEA,OAAO,KAAK,IAAI;IAClB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,2DAA2D;QAC3D,IAAI,MAAM,QAAQ,EAAE;YAClB,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ,CAAC,MAAM;YACvD,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YAEnD,mEAAmE;YACnE,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;gBACtD,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C;QACF;QAEA,MAAM;IACR;AACF;AAGO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,SAAS;QACjE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAQO,MAAM,cAAc,OACzB,SACA,OACA,SACA;IAEA,IAAI;QACF,wCAAwC;QACxC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,MAAM,UAAU;YAC9B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,KAAK,GAAG;YAC/D,MAAM,IAAI,MAAM;QAClB;QAEA,oDAAoD;QACpD,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI;QAElD,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,IAAI,MAAM;YACR,MAAM,cAAc,KAAK,MAAM,CAC7B,CAAC,MAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI;YAE9C,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,sCAAsC;QACtC,sDAAsD;QACtD,MAAM,cAAc,IAAI;QACxB,MAAM,oBAAoB,IAAI;QAE9B,iCAAiC;QACjC,QAAQ,OAAO,CAAC,CAAC,KAAK;YACpB,IAAI,IAAI,EAAE,EAAE;gBACV,YAAY,GAAG,CAAC,IAAI,EAAE,EAAE;YAC1B;YACA,yBAAyB;YACzB,kBAAkB,GAAG,CAAC,QAAQ,GAAG;QACnC;QAEA,kCAAkC;QAClC,KAAK,MAAM,OAAO,QAAS;YACzB,IAAI,IAAI,cAAc,EAAE;gBACtB,6CAA6C;gBAC7C,IAAI,IAAI,cAAc,IAAI,GAAG;oBAC3B,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,IAAI,cAAc,CAAC,4BAA4B,CAAC;gBAEjF;gBAEA,iCAAiC;gBACjC,IAAI,YAAY,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,IAAI,cAAc;gBAE/D,gEAAgE;gBAChE,IAAI,CAAC,aAAa,IAAI,cAAc,IAAI,QAAQ,MAAM,EAAE;oBACtD,YAAY,kBAAkB,GAAG,CAAC,IAAI,cAAc;oBACpD,QAAQ,GAAG,CACT,CAAC,yBAAyB,EAAE,IAAI,cAAc,CAAC,EAAE,EAAE,WAAW,YAAY;gBAE9E;gBAEA,mDAAmD;gBACnD,IAAI,CAAC,WAAW;oBACd,MAAM,IAAI,MACR,CAAC,+BAA+B,EAAE,IAAI,cAAc,CAAC,gCAAgC,CAAC;gBAE1F;gBAEA,gCAAgC;gBAChC,kEAAkE;gBAClE,sDAAsD;gBACtD,IAAI,UAAU,cAAc,EAAE;oBAC5B,MAAM,IAAI,MACR;gBAEJ;YACF;QACF;QAEA,4EAA4E;QAC5E,mDAAmD;QACnD,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,+DAA+D;QAC/D,MAAM,iBAIA,QAAQ,GAAG,CAAC,CAAC;YACjB,MAAM,WAIF;gBACF,YAAY,IAAI,UAAU,CAAC,IAAI;YACjC;YAEA,IAAI,IAAI,EAAE,EAAE;gBACV,SAAS,EAAE,GAAG,IAAI,EAAE;YACtB;YAEA,IAAI,IAAI,cAAc,KAAK,WAAW;gBACpC,SAAS,cAAc,GAAG,IAAI,cAAc;YAC9C;YAEA,OAAO;QACT;QAEA,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,QAAQ,GAAG,CAAC,6BAA6B;YACvC;YACA;YACA,SAAS;YACT;QACF;QAEA,uDAAuD;QACvD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,SAAS,EAAE;gBAChE,OAAO,MAAM,IAAI;gBACjB,SAAS;gBACT,MAAM,OACF,KAAK,GAAG,CAAC,CAAC,MAAQ,CAAC;wBACjB,GAAG,GAAG;wBACN,UAAU,IAAI,QAAQ,CAAC,IAAI;oBAC7B,CAAC,KACD,EAAE;YACR;YAEA,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;gBAC1B,MAAM,IAAI,MAAM,MAAM,WAAW;YACnC;YAEA,OAAO,KAAK,IAAI;QAClB,EAAE,OAAO,UAAe;YACtB,QAAQ,KAAK,CAAC,6BAA6B;YAE3C,2DAA2D;YAC3D,IAAI,SAAS,QAAQ,EAAE;gBACrB,QAAQ,KAAK,CAAC,oBAAoB,SAAS,QAAQ,CAAC,MAAM;gBAC1D,QAAQ,KAAK,CAAC,kBAAkB,SAAS,QAAQ,CAAC,IAAI;gBAEtD,mEAAmE;gBACnE,IAAI,SAAS,QAAQ,CAAC,IAAI,IAAI,SAAS,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;oBAC5D,MAAM,IAAI,MAAM,SAAS,QAAQ,CAAC,IAAI,CAAC,OAAO;gBAChD;YACF;YAEA,sEAAsE;YACtE,MAAM;QACR;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,yCAAyC;QACzC,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;QAC5D,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/form-inputs/TableInput.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, {\r\n  useState,\r\n  useEffect,\r\n  use<PERSON><PERSON>back,\r\n  useMemo,\r\n  useRef,\r\n} from \"react\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow as UITableRow,\r\n} from \"@/components/ui/table\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  fetchTableStructure,\r\n  TableRow as TableRowType,\r\n  CellValue,\r\n  DefaultValue,\r\n} from \"../../lib/api/table\";\r\nimport { Plus, Trash2 } from \"lucide-react\";\r\nimport debounce from \"lodash/debounce\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\n\r\n// Define TableColumn interface to ensure childColumns is optional\r\ninterface TableColumn {\r\n  id: number;\r\n  columnName: string;\r\n  parentColumnId?: number | null;\r\n  childColumns?: TableColumn[];\r\n}\r\n\r\ninterface TableInputProps {\r\n  questionId: number;\r\n  value: string | CellValue[];\r\n  onChange: (value: CellValue[]) => void;\r\n  required?: boolean;\r\n}\r\n\r\n// Memoize the TableInput component to prevent unnecessary re-renders\r\nexport const TableInput = React.memo(\r\n  ({ questionId, value, onChange, required = false }: TableInputProps) => {\r\n    // State declarations\r\n    const [columns, setColumns] = useState<TableColumn[]>([]);\r\n    const [rows, setRows] = useState<TableRowType[]>([]);\r\n    const [cellValues, setCellValues] = useState<Record<string, string>>({});\r\n    const [userInputValues, setUserInputValues] = useState<\r\n      Record<string, string>\r\n    >({});\r\n\r\n    // Ref to track cellValues for responsive input\r\n    const cellValuesRef = useRef<Record<string, string>>(cellValues);\r\n\r\n    // Update ref whenever cellValues changes\r\n    useEffect(() => {\r\n      cellValuesRef.current = cellValues;\r\n    }, [cellValues]);\r\n\r\n    // Process columns to create a flat structure with parent-child relationships\r\n    const processColumns = useCallback(\r\n      (tableData: { tableColumns?: TableColumn[] }): TableColumn[] => {\r\n        if (!tableData?.tableColumns) return [];\r\n\r\n        const flattenedColumns: TableColumn[] = [];\r\n        const parentColumns = tableData.tableColumns.filter(\r\n          (col: TableColumn) =>\r\n            col.parentColumnId === null || col.parentColumnId === undefined\r\n        );\r\n\r\n        parentColumns.forEach((parentCol: TableColumn) => {\r\n          flattenedColumns.push(parentCol);\r\n          const childColumns = parentCol.childColumns;\r\n          if (\r\n            childColumns &&\r\n            Array.isArray(childColumns) &&\r\n            childColumns.length > 0\r\n          ) {\r\n            childColumns.forEach((childCol: TableColumn) => {\r\n              flattenedColumns.push({\r\n                id: childCol.id,\r\n                columnName: childCol.columnName,\r\n                parentColumnId: childCol.parentColumnId,\r\n              });\r\n            });\r\n          }\r\n        });\r\n\r\n        return flattenedColumns;\r\n      },\r\n      []\r\n    );\r\n\r\n    // Memoize grouped columns to avoid recomputation on every render\r\n    const groupedColumns = useMemo(() => {\r\n      if (!columns.length) {\r\n        return {\r\n          parentColumns: [],\r\n          columnMap: new Map<number, TableColumn[]>(),\r\n          hasChildColumns: false,\r\n        };\r\n      }\r\n\r\n      const parentColumns = columns.filter((col) => !col.parentColumnId);\r\n      const columnMap = new Map<number, TableColumn[]>();\r\n\r\n      parentColumns.forEach((parentCol) => {\r\n        const childColumns = columns.filter(\r\n          (col) => col.parentColumnId === parentCol.id\r\n        );\r\n        columnMap.set(parentCol.id, childColumns);\r\n      });\r\n\r\n      const hasChildColumns = parentColumns.some(\r\n        (p) => (columnMap.get(p.id) ?? []).length > 0\r\n      );\r\n\r\n      return { parentColumns, columnMap, hasChildColumns };\r\n    }, [columns]);\r\n\r\n    // Use React Query to fetch table structure with automatic refetching\r\n    const {\r\n      data: tableData,\r\n      isLoading: loading,\r\n      error: queryError,\r\n    } = useQuery({\r\n      queryKey: [\"tableStructure\", questionId],\r\n      queryFn: () => fetchTableStructure(questionId),\r\n      enabled: questionId > 0,\r\n      staleTime: 0, // Always consider data stale to ensure fresh fetches\r\n      gcTime: 0, // Don't cache the data\r\n    });\r\n\r\n    // Set error state based on query error\r\n    const error = queryError ? \"Failed to load table structure\" : null;\r\n\r\n    // Process table data when it changes\r\n    useEffect(() => {\r\n      if (!tableData) {\r\n        setColumns([]);\r\n        setRows([]);\r\n        setCellValues({});\r\n        return;\r\n      }\r\n\r\n      console.log(\"Processing table data:\", tableData);\r\n      setColumns(processColumns(tableData));\r\n      setRows(tableData.tableRows || []);\r\n\r\n      // Process default values from rows and cell values\r\n      const defaultCellValues: Record<string, string> = {};\r\n      const defaultCellKeys: string[] = [];\r\n\r\n      // Define the type for cell values\r\n      interface CellValueWithDefault {\r\n        value: string;\r\n        isDefault: boolean;\r\n      }\r\n\r\n      // Check if we have cell values in the response\r\n      if (tableData.cellValues) {\r\n        console.log(\"Cell values found in response:\", tableData.cellValues);\r\n        // Process cell values from the response\r\n        (\r\n          Object.entries(tableData.cellValues) as [\r\n            string,\r\n            string | CellValueWithDefault\r\n          ][]\r\n        ).forEach(([key, value]) => {\r\n          if (typeof value === \"object\" && value !== null) {\r\n            // Handle object format with value property\r\n            const cellValue = value.value || \"\";\r\n            if (cellValue.trim() !== \"\") {\r\n              defaultCellValues[key] = cellValue;\r\n              defaultCellKeys.push(key);\r\n              console.log(\r\n                `Loading cell value from object for ${key}: ${cellValue} (isDefault: ${value.isDefault})`\r\n              );\r\n            }\r\n          } else if (typeof value === \"string\" && value.trim() !== \"\") {\r\n            // Handle string format\r\n            defaultCellValues[key] = value;\r\n            defaultCellKeys.push(key);\r\n            console.log(`Loading cell value from string for ${key}: ${value}`);\r\n          }\r\n        });\r\n      }\r\n\r\n      // Also check if tableRows have defaultValues (for backward compatibility)\r\n      if (tableData.tableRows && tableData.tableRows.length > 0) {\r\n        tableData.tableRows.forEach((row: TableRowType) => {\r\n          // Check if defaultValues exists on the row\r\n          // In some API responses, rows might not have the defaultValues property\r\n          if (\r\n            row.defaultValues &&\r\n            Array.isArray(row.defaultValues) &&\r\n            row.defaultValues.length > 0\r\n          ) {\r\n            row.defaultValues.forEach((dv: DefaultValue) => {\r\n              const key = `${dv.columnId}_${row.id}`;\r\n              defaultCellValues[key] = dv.value;\r\n              defaultCellKeys.push(key);\r\n              console.log(\r\n                `Loading default value from row for ${key}: ${dv.value}`\r\n              );\r\n            });\r\n          } else {\r\n            console.log(\r\n              `Row ${row.id} has no defaultValues property or it's empty`\r\n            );\r\n          }\r\n        });\r\n      }\r\n\r\n      // Set default values in the cellValues state\r\n      if (Object.keys(defaultCellValues).length > 0) {\r\n        console.log(\"Setting cell values:\", defaultCellValues);\r\n        console.log(\"Default cell keys:\", defaultCellKeys);\r\n\r\n        // Force a re-render by creating a new object reference\r\n        const newCellValues = { ...defaultCellValues };\r\n\r\n        // Log each cell value for debugging\r\n        Object.entries(newCellValues).forEach(([key, value]) => {\r\n          console.log(`Setting cell value for ${key}: ${value}`);\r\n        });\r\n\r\n        // Set the cell values directly without merging with previous state\r\n        setCellValues(newCellValues);\r\n\r\n        // Wait a moment and then set the values again to ensure they're applied\r\n        setTimeout(() => {\r\n          console.log(\"Re-applying cell values to ensure they are set\");\r\n          setCellValues((prevValues) => ({ ...prevValues }));\r\n        }, 100);\r\n      } else {\r\n        console.warn(\"No default or cell values found for this table\");\r\n        setCellValues({});\r\n      }\r\n    }, [tableData, processColumns]);\r\n\r\n    // Handle initial value parsing and form reset\r\n    useEffect(() => {\r\n      if (loading) return;\r\n\r\n      const initialCellValues: Record<string, string> = {};\r\n      let cellData: CellValue[] = [];\r\n\r\n      if (typeof value === \"string\" && value.trim()) {\r\n        try {\r\n          cellData = JSON.parse(value);\r\n        } catch {\r\n          cellData = [];\r\n        }\r\n      } else if (Array.isArray(value)) {\r\n        cellData = value;\r\n      }\r\n\r\n      cellData.forEach((cell) => {\r\n        initialCellValues[`${cell.columnId}_${cell.rowsId}`] = cell.value;\r\n      });\r\n\r\n      const isFormReset = !value || (Array.isArray(value) && !value.length);\r\n\r\n      if (isFormReset) {\r\n        setUserInputValues({});\r\n      } else if (Object.keys(initialCellValues).length) {\r\n        setUserInputValues((prev) => ({ ...prev, ...initialCellValues }));\r\n      }\r\n    }, [value, loading]);\r\n\r\n    // Effect to ensure default values are always included in form submission\r\n    useEffect(() => {\r\n      if (!loading && Object.keys(cellValues).length > 0) {\r\n        // Combine default values and user input values\r\n        const allValues = { ...cellValues, ...userInputValues };\r\n\r\n        const updatedCellValues: CellValue[] = Object.entries(allValues)\r\n          .filter(([, value]) => value.trim())\r\n          .map(([key, value]) => {\r\n            const [colId, rowId] = key.split(\"_\").map(Number);\r\n            return { columnId: colId, rowsId: rowId, value };\r\n          });\r\n\r\n        // Only update if there are values to submit\r\n        if (updatedCellValues.length > 0) {\r\n          onChange(updatedCellValues);\r\n        }\r\n      }\r\n    }, [cellValues, userInputValues, loading, onChange]);\r\n\r\n    // Debounced cell change handler to reduce rapid state updates\r\n    const handleCellChange = useCallback(\r\n      debounce(\r\n        (columnId: number, rowId: number | string, newValue: string) => {\r\n          // Always combine default values and user input values for submission\r\n          const allValues = { ...cellValues, ...userInputValues };\r\n          allValues[`${columnId}_${rowId}`] = newValue;\r\n\r\n          const updatedCellValues: CellValue[] = Object.entries(allValues)\r\n            .filter(([, value]) => value.trim())\r\n            .map(([key, value]) => {\r\n              const [colId, rowId] = key.split(\"_\").map(Number);\r\n              return { columnId: colId, rowsId: rowId, value };\r\n            });\r\n\r\n          onChange(updatedCellValues);\r\n        },\r\n        300,\r\n        { leading: false, trailing: true }\r\n      ),\r\n      [onChange, cellValues, userInputValues]\r\n    );\r\n\r\n    // Handle input change with immediate local state update for responsiveness\r\n    const handleInputChange = useCallback(\r\n      (columnId: number, rowId: number | string, newValue: string) => {\r\n        // Update user input values (separate from default values)\r\n        setUserInputValues((prev) => ({\r\n          ...prev,\r\n          [`${columnId}_${rowId}`]: newValue,\r\n        }));\r\n        // Trigger debounced update\r\n        handleCellChange(columnId, rowId, newValue);\r\n      },\r\n      [handleCellChange]\r\n    );\r\n\r\n    // Cleanup debounce on unmount\r\n    useEffect(() => {\r\n      return () => {\r\n        handleCellChange.cancel();\r\n      };\r\n    }, [handleCellChange]);\r\n\r\n    // Add a new row\r\n    const handleAddRow = useCallback((e: React.MouseEvent) => {\r\n      e.preventDefault();\r\n      e.stopPropagation();\r\n\r\n      setRows((prevRows) => [\r\n        ...prevRows,\r\n        {\r\n          id: -(prevRows.length + 1),\r\n          rowsName: (prevRows.length + 1).toString(),\r\n        },\r\n      ]);\r\n    }, []);\r\n\r\n    // Remove a row and clean up cell values\r\n    const handleRemoveRow = useCallback(\r\n      (rowIndex: number) => {\r\n        if (rows.length <= 1) return;\r\n\r\n        const rowToRemove = rows[rowIndex];\r\n\r\n        // Don't allow deletion of rows that came from the table question builder (positive IDs)\r\n        // Only allow deletion of dynamically added rows (negative IDs)\r\n        if (rowToRemove.id > 0) {\r\n          console.log(\r\n            `Cannot delete row ${rowToRemove.id} - it's from the table question builder`\r\n          );\r\n          return;\r\n        }\r\n\r\n        setRows((prevRows) =>\r\n          prevRows\r\n            .filter((_, index) => index !== rowIndex)\r\n            .map((row, idx) => ({\r\n              ...row,\r\n              rowsName: (idx + 1).toString(),\r\n            }))\r\n        );\r\n\r\n        setUserInputValues((prevValues) => {\r\n          const newValues = { ...prevValues };\r\n          Object.keys(newValues).forEach((key) => {\r\n            const [_, rowId] = key.split(\"_\");\r\n            if (rowId === rowToRemove.id.toString()) {\r\n              delete newValues[key];\r\n            }\r\n          });\r\n\r\n          // Combine with default values for submission\r\n          const allValues = { ...cellValues, ...newValues };\r\n          const updatedCellValues: CellValue[] = Object.entries(allValues)\r\n            .filter(([, value]) => value.trim())\r\n            .map(([key, value]) => {\r\n              const [colId, rowId] = key.split(\"_\").map(Number);\r\n              return { columnId: colId, rowsId: rowId, value };\r\n            });\r\n\r\n          onChange(updatedCellValues);\r\n          return newValues;\r\n        });\r\n      },\r\n      [rows, onChange, cellValues]\r\n    );\r\n\r\n    // Memoize the table body to prevent re-rendering unchanged rows\r\n    // Note: For large tables (>50 rows), consider using react-window for virtualization:\r\n    // import { FixedSizeList } from \"react-window\";\r\n    // <FixedSizeList height={400} itemCount={rows.length} itemSize={48} width=\"100%\">\r\n    //   {({ index, style }) => <Row index={index} style={style} />}\r\n    // </FixedSizeList>\r\n    const tableBody = useMemo(() => {\r\n      if (!rows.length) {\r\n        return (\r\n          <UITableRow>\r\n            <TableCell className=\"border p-1 text-center font-medium bg-blue-50/50\">\r\n              Row 1\r\n            </TableCell>\r\n            {groupedColumns.parentColumns.map((parentCol) => {\r\n              const childColumns =\r\n                groupedColumns.columnMap.get(parentCol.id) || [];\r\n              if (!childColumns.length) {\r\n                const cellValue = cellValues[`${parentCol.id}_no_row`] || \"\";\r\n                const hasDefaultValue = cellValue.trim() !== \"\";\r\n\r\n                return (\r\n                  <TableCell\r\n                    key={`cell-${parentCol.id}-no-row`}\r\n                    className=\"border p-1\"\r\n                  >\r\n                    {hasDefaultValue ? (\r\n                      // Show default value as static text with normal input styling\r\n                      <div className=\"w-full border-0 bg-transparent p-2 min-h-[38px] text-gray-900\">\r\n                        {cellValue}\r\n                      </div>\r\n                    ) : (\r\n                      // Show input field for user entry\r\n                      <Input\r\n                        value={userInputValues[`${parentCol.id}_no_row`] || \"\"}\r\n                        onChange={(e) =>\r\n                          handleInputChange(\r\n                            parentCol.id,\r\n                            \"no_row\",\r\n                            e.target.value\r\n                          )\r\n                        }\r\n                        placeholder=\"Enter value\"\r\n                        className=\"w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500\"\r\n                      />\r\n                    )}\r\n                  </TableCell>\r\n                );\r\n              }\r\n              return childColumns.map((childCol) => {\r\n                const cellValue = cellValues[`${childCol.id}_no_row`] || \"\";\r\n                const hasDefaultValue = cellValue.trim() !== \"\";\r\n\r\n                return (\r\n                  <TableCell\r\n                    key={`cell-${childCol.id}-no-row`}\r\n                    className=\"border p-1\"\r\n                  >\r\n                    {hasDefaultValue ? (\r\n                      // Show default value as static text with normal input styling\r\n                      <div className=\"w-full border-0 bg-transparent p-2 min-h-[38px] text-gray-900\">\r\n                        {cellValue}\r\n                      </div>\r\n                    ) : (\r\n                      // Show input field for user entry\r\n                      <Input\r\n                        value={userInputValues[`${childCol.id}_no_row`] || \"\"}\r\n                        onChange={(e) =>\r\n                          handleInputChange(\r\n                            childCol.id,\r\n                            \"no_row\",\r\n                            e.target.value\r\n                          )\r\n                        }\r\n                        placeholder=\"Enter value\"\r\n                        className=\"w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500\"\r\n                      />\r\n                    )}\r\n                  </TableCell>\r\n                );\r\n              });\r\n            })}\r\n            <TableCell className=\"border p-1 w-10\"></TableCell>\r\n          </UITableRow>\r\n        );\r\n      }\r\n\r\n      return rows.map((row, rowIndex) => (\r\n        <UITableRow\r\n          key={row.id}\r\n          className={rowIndex % 2 === 0 ? \"bg-white\" : \"bg-gray-50\"}\r\n        >\r\n          <TableCell className=\"border p-1 text-center font-medium bg-blue-50/50\">\r\n            {rowIndex + 1}\r\n          </TableCell>\r\n          {groupedColumns.parentColumns.map((parentCol) => {\r\n            const childColumns =\r\n              groupedColumns.columnMap.get(parentCol.id) || [];\r\n            if (!childColumns.length) {\r\n              // Make sure we're getting the cell value correctly\r\n              const cellKey = `${parentCol.id}_${row.id}`;\r\n              const cellValue = cellValues[cellKey] || \"\";\r\n              const hasDefaultValue = cellValue.trim() !== \"\";\r\n\r\n              return (\r\n                <TableCell\r\n                  key={`cell-${parentCol.id}-${row.id}`}\r\n                  className=\"border p-1\"\r\n                >\r\n                  {hasDefaultValue ? (\r\n                    // Show default value as static text with normal input styling\r\n                    <div className=\"w-full border-0 bg-transparent p-2 min-h-[38px] text-gray-900\">\r\n                      {cellValue}\r\n                    </div>\r\n                  ) : (\r\n                    // Show input field for user entry\r\n                    <Input\r\n                      value={userInputValues[`${parentCol.id}_${row.id}`] || \"\"}\r\n                      onChange={(e) =>\r\n                        handleInputChange(parentCol.id, row.id, e.target.value)\r\n                      }\r\n                      placeholder=\"Enter value\"\r\n                      className=\"w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500\"\r\n                    />\r\n                  )}\r\n                </TableCell>\r\n              );\r\n            }\r\n            return childColumns.map((childCol) => {\r\n              const cellKey = `${childCol.id}_${row.id}`;\r\n              const cellValue = cellValues[cellKey] || \"\";\r\n              const hasDefaultValue = cellValue.trim() !== \"\";\r\n\r\n              return (\r\n                <TableCell\r\n                  key={`cell-${childCol.id}-${row.id}`}\r\n                  className=\"border p-1\"\r\n                >\r\n                  {hasDefaultValue ? (\r\n                    // Show default value as static text with normal input styling\r\n                    <div className=\"w-full border-0 bg-transparent p-2 min-h-[38px] text-gray-900\">\r\n                      {cellValue}\r\n                    </div>\r\n                  ) : (\r\n                    // Show input field for user entry\r\n                    <Input\r\n                      value={userInputValues[`${childCol.id}_${row.id}`] || \"\"}\r\n                      onChange={(e) =>\r\n                        handleInputChange(childCol.id, row.id, e.target.value)\r\n                      }\r\n                      placeholder=\"Enter value\"\r\n                      className=\"w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500\"\r\n                    />\r\n                  )}\r\n                </TableCell>\r\n              );\r\n            });\r\n          })}\r\n          <TableCell className=\"border p-1 w-10\">\r\n            <button\r\n              type=\"button\"\r\n              className={`${\r\n                rows.length <= 1 || row.id > 0\r\n                  ? \"text-gray-300 cursor-not-allowed\"\r\n                  : \"text-red-500 hover:text-red-700\"\r\n              }`}\r\n              onClick={() => handleRemoveRow(rowIndex)}\r\n              disabled={rows.length <= 1 || row.id > 0}\r\n              aria-label={\r\n                row.id > 0 ? \"Cannot delete table question row\" : \"Delete Row\"\r\n              }\r\n              title={\r\n                row.id > 0\r\n                  ? \"This row is from the table question and cannot be deleted\"\r\n                  : \"Delete this row\"\r\n              }\r\n            >\r\n              <Trash2 className=\"w-4 h-4\" />\r\n            </button>\r\n          </TableCell>\r\n        </UITableRow>\r\n      ));\r\n    }, [\r\n      rows,\r\n      cellValues,\r\n      userInputValues,\r\n      groupedColumns,\r\n      required,\r\n      handleInputChange,\r\n      handleRemoveRow,\r\n    ]);\r\n\r\n    const hasNoColumns = !columns.length;\r\n\r\n    return (\r\n      <div className=\"overflow-x-auto\">\r\n        {loading ? (\r\n          <div className=\"py-4 text-center\">Loading table...</div>\r\n        ) : error ? (\r\n          <div className=\"py-4 text-center text-red-500\">{error}</div>\r\n        ) : hasNoColumns ? (\r\n          <div className=\"py-4 text-center text-amber-600\">\r\n            This table has no columns defined. Please configure the table\r\n            question first.\r\n          </div>\r\n        ) : (\r\n          <>\r\n            <Table className=\"border-collapse\">\r\n              <TableHeader>\r\n                <UITableRow>\r\n                  <TableHead\r\n                    className=\"text-center border bg-blue-50 font-medium\"\r\n                    rowSpan={groupedColumns.hasChildColumns ? 2 : 1}\r\n                  >\r\n                    S.No.\r\n                  </TableHead>\r\n                  {groupedColumns.parentColumns.map((parentCol) => {\r\n                    const childColumns =\r\n                      groupedColumns.columnMap.get(parentCol.id) || [];\r\n                    const colSpan = childColumns.length || 1;\r\n                    return (\r\n                      <TableHead\r\n                        key={parentCol.id}\r\n                        colSpan={colSpan}\r\n                        className=\"text-center border bg-blue-50 font-medium\"\r\n                        rowSpan={childColumns.length === 0 ? 2 : 1}\r\n                      >\r\n                        {parentCol.columnName}\r\n                      </TableHead>\r\n                    );\r\n                  })}\r\n                </UITableRow>\r\n                {groupedColumns.hasChildColumns && (\r\n                  <UITableRow>\r\n                    {groupedColumns.parentColumns.map((parentCol) => {\r\n                      const childColumns =\r\n                        groupedColumns.columnMap.get(parentCol.id) || [];\r\n                      if (!childColumns.length) return null;\r\n                      return childColumns.map((childCol) => (\r\n                        <TableHead\r\n                          key={childCol.id}\r\n                          className=\"border bg-blue-50/50 text-sm\"\r\n                        >\r\n                          {childCol.columnName}\r\n                        </TableHead>\r\n                      ));\r\n                    })}\r\n                  </UITableRow>\r\n                )}\r\n              </TableHeader>\r\n              <TableBody>{tableBody}</TableBody>\r\n            </Table>\r\n            <div className=\"mt-2 flex justify-end\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleAddRow}\r\n                className=\"border rounded px-3 py-1 text-sm flex items-center gap-1 hover:bg-blue-100\"\r\n              >\r\n                <Plus className=\"h-4 w-4\" /> Add Row\r\n              </button>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n);\r\n"], "names": [], "mappings": ";;;;AAEA;AAOA;AAQA;AACA;AAMA;AAAA;AACA;AACA;;;AA1BA;;;;;;;;AA4CO,MAAM,2BAAa,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,SAClC,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAmB;;IACjE,qBAAqB;IACrB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACxD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEnD,CAAC;IAEH,+CAA+C;IAC/C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA0B;IAErD,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,cAAc,OAAO,GAAG;QAC1B;+BAAG;QAAC;KAAW;IAEf,6EAA6E;IAC7E,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAC/B,CAAC;YACC,IAAI,CAAC,WAAW,cAAc,OAAO,EAAE;YAEvC,MAAM,mBAAkC,EAAE;YAC1C,MAAM,gBAAgB,UAAU,YAAY,CAAC,MAAM;wEACjD,CAAC,MACC,IAAI,cAAc,KAAK,QAAQ,IAAI,cAAc,KAAK;;YAG1D,cAAc,OAAO;0DAAC,CAAC;oBACrB,iBAAiB,IAAI,CAAC;oBACtB,MAAM,eAAe,UAAU,YAAY;oBAC3C,IACE,gBACA,MAAM,OAAO,CAAC,iBACd,aAAa,MAAM,GAAG,GACtB;wBACA,aAAa,OAAO;sEAAC,CAAC;gCACpB,iBAAiB,IAAI,CAAC;oCACpB,IAAI,SAAS,EAAE;oCACf,YAAY,SAAS,UAAU;oCAC/B,gBAAgB,SAAS,cAAc;gCACzC;4BACF;;oBACF;gBACF;;YAEA,OAAO;QACT;iDACA,EAAE;IAGJ,iEAAiE;IACjE,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YAC7B,IAAI,CAAC,QAAQ,MAAM,EAAE;gBACnB,OAAO;oBACL,eAAe,EAAE;oBACjB,WAAW,IAAI;oBACf,iBAAiB;gBACnB;YACF;YAEA,MAAM,gBAAgB,QAAQ,MAAM;oEAAC,CAAC,MAAQ,CAAC,IAAI,cAAc;;YACjE,MAAM,YAAY,IAAI;YAEtB,cAAc,OAAO;sDAAC,CAAC;oBACrB,MAAM,eAAe,QAAQ,MAAM;2EACjC,CAAC,MAAQ,IAAI,cAAc,KAAK,UAAU,EAAE;;oBAE9C,UAAU,GAAG,CAAC,UAAU,EAAE,EAAE;gBAC9B;;YAEA,MAAM,kBAAkB,cAAc,IAAI;sEACxC,CAAC,IAAM,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,GAAG;;YAG9C,OAAO;gBAAE;gBAAe;gBAAW;YAAgB;QACrD;6CAAG;QAAC;KAAQ;IAEZ,qEAAqE;IACrE,MAAM,EACJ,MAAM,SAAS,EACf,WAAW,OAAO,EAClB,OAAO,UAAU,EAClB,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACX,UAAU;YAAC;YAAkB;SAAW;QACxC,OAAO;mCAAE,IAAM,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;;QACnC,SAAS,aAAa;QACtB,WAAW;QACX,QAAQ;IACV;IAEA,uCAAuC;IACvC,MAAM,QAAQ,aAAa,mCAAmC;IAE9D,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,WAAW;gBACd,WAAW,EAAE;gBACb,QAAQ,EAAE;gBACV,cAAc,CAAC;gBACf;YACF;YAEA,QAAQ,GAAG,CAAC,0BAA0B;YACtC,WAAW,eAAe;YAC1B,QAAQ,UAAU,SAAS,IAAI,EAAE;YAEjC,mDAAmD;YACnD,MAAM,oBAA4C,CAAC;YACnD,MAAM,kBAA4B,EAAE;YAQpC,+CAA+C;YAC/C,IAAI,UAAU,UAAU,EAAE;gBACxB,QAAQ,GAAG,CAAC,kCAAkC,UAAU,UAAU;gBAClE,wCAAwC;gBAEtC,OAAO,OAAO,CAAC,UAAU,UAAU,EAInC,OAAO;4CAAC,CAAC,CAAC,KAAK,MAAM;wBACrB,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;4BAC/C,2CAA2C;4BAC3C,MAAM,YAAY,MAAM,KAAK,IAAI;4BACjC,IAAI,UAAU,IAAI,OAAO,IAAI;gCAC3B,iBAAiB,CAAC,IAAI,GAAG;gCACzB,gBAAgB,IAAI,CAAC;gCACrB,QAAQ,GAAG,CACT,CAAC,mCAAmC,EAAE,IAAI,EAAE,EAAE,UAAU,aAAa,EAAE,MAAM,SAAS,CAAC,CAAC,CAAC;4BAE7F;wBACF,OAAO,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,IAAI;4BAC3D,uBAAuB;4BACvB,iBAAiB,CAAC,IAAI,GAAG;4BACzB,gBAAgB,IAAI,CAAC;4BACrB,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,IAAI,EAAE,EAAE,OAAO;wBACnE;oBACF;;YACF;YAEA,0EAA0E;YAC1E,IAAI,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,MAAM,GAAG,GAAG;gBACzD,UAAU,SAAS,CAAC,OAAO;4CAAC,CAAC;wBAC3B,2CAA2C;wBAC3C,wEAAwE;wBACxE,IACE,IAAI,aAAa,IACjB,MAAM,OAAO,CAAC,IAAI,aAAa,KAC/B,IAAI,aAAa,CAAC,MAAM,GAAG,GAC3B;4BACA,IAAI,aAAa,CAAC,OAAO;wDAAC,CAAC;oCACzB,MAAM,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;oCACtC,iBAAiB,CAAC,IAAI,GAAG,GAAG,KAAK;oCACjC,gBAAgB,IAAI,CAAC;oCACrB,QAAQ,GAAG,CACT,CAAC,mCAAmC,EAAE,IAAI,EAAE,EAAE,GAAG,KAAK,EAAE;gCAE5D;;wBACF,OAAO;4BACL,QAAQ,GAAG,CACT,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,4CAA4C,CAAC;wBAE/D;oBACF;;YACF;YAEA,6CAA6C;YAC7C,IAAI,OAAO,IAAI,CAAC,mBAAmB,MAAM,GAAG,GAAG;gBAC7C,QAAQ,GAAG,CAAC,wBAAwB;gBACpC,QAAQ,GAAG,CAAC,sBAAsB;gBAElC,uDAAuD;gBACvD,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;gBAAC;gBAE7C,oCAAoC;gBACpC,OAAO,OAAO,CAAC,eAAe,OAAO;4CAAC,CAAC,CAAC,KAAK,MAAM;wBACjD,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,IAAI,EAAE,EAAE,OAAO;oBACvD;;gBAEA,mEAAmE;gBACnE,cAAc;gBAEd,wEAAwE;gBACxE;4CAAW;wBACT,QAAQ,GAAG,CAAC;wBACZ;oDAAc,CAAC,aAAe,CAAC;oCAAE,GAAG,UAAU;gCAAC,CAAC;;oBAClD;2CAAG;YACL,OAAO;gBACL,QAAQ,IAAI,CAAC;gBACb,cAAc,CAAC;YACjB;QACF;+BAAG;QAAC;QAAW;KAAe;IAE9B,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,SAAS;YAEb,MAAM,oBAA4C,CAAC;YACnD,IAAI,WAAwB,EAAE;YAE9B,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI,IAAI;gBAC7C,IAAI;oBACF,WAAW,KAAK,KAAK,CAAC;gBACxB,EAAE,OAAM;oBACN,WAAW,EAAE;gBACf;YACF,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;gBAC/B,WAAW;YACb;YAEA,SAAS,OAAO;wCAAC,CAAC;oBAChB,iBAAiB,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE,CAAC,GAAG,KAAK,KAAK;gBACnE;;YAEA,MAAM,cAAc,CAAC,SAAU,MAAM,OAAO,CAAC,UAAU,CAAC,MAAM,MAAM;YAEpE,IAAI,aAAa;gBACf,mBAAmB,CAAC;YACtB,OAAO,IAAI,OAAO,IAAI,CAAC,mBAAmB,MAAM,EAAE;gBAChD;4CAAmB,CAAC,OAAS,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,iBAAiB;wBAAC,CAAC;;YACjE;QACF;+BAAG;QAAC;QAAO;KAAQ;IAEnB,yEAAyE;IACzE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,GAAG;gBAClD,+CAA+C;gBAC/C,MAAM,YAAY;oBAAE,GAAG,UAAU;oBAAE,GAAG,eAAe;gBAAC;gBAEtD,MAAM,oBAAiC,OAAO,OAAO,CAAC,WACnD,MAAM;8DAAC,CAAC,GAAG,MAAM,GAAK,MAAM,IAAI;6DAChC,GAAG;8DAAC,CAAC,CAAC,KAAK,MAAM;wBAChB,MAAM,CAAC,OAAO,MAAM,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC;wBAC1C,OAAO;4BAAE,UAAU;4BAAO,QAAQ;4BAAO;wBAAM;oBACjD;;gBAEF,4CAA4C;gBAC5C,IAAI,kBAAkB,MAAM,GAAG,GAAG;oBAChC,SAAS;gBACX;YACF;QACF;+BAAG;QAAC;QAAY;QAAiB;QAAS;KAAS;IAEnD,8DAA8D;IAC9D,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EACjC,CAAA,GAAA,qIAAA,CAAA,UAAQ,AAAD;oDACL,CAAC,UAAkB,OAAwB;YACzC,qEAAqE;YACrE,MAAM,YAAY;gBAAE,GAAG,UAAU;gBAAE,GAAG,eAAe;YAAC;YACtD,SAAS,CAAC,GAAG,SAAS,CAAC,EAAE,OAAO,CAAC,GAAG;YAEpC,MAAM,oBAAiC,OAAO,OAAO,CAAC,WACnD,MAAM;8EAAC,CAAC,GAAG,MAAM,GAAK,MAAM,IAAI;6EAChC,GAAG;8EAAC,CAAC,CAAC,KAAK,MAAM;oBAChB,MAAM,CAAC,OAAO,MAAM,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC;oBAC1C,OAAO;wBAAE,UAAU;wBAAO,QAAQ;wBAAO;oBAAM;gBACjD;;YAEF,SAAS;QACX;mDACA,KACA;QAAE,SAAS;QAAO,UAAU;IAAK,IAEnC;QAAC;QAAU;QAAY;KAAgB;IAGzC,2EAA2E;IAC3E,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAClC,CAAC,UAAkB,OAAwB;YACzC,0DAA0D;YAC1D;6DAAmB,CAAC,OAAS,CAAC;wBAC5B,GAAG,IAAI;wBACP,CAAC,GAAG,SAAS,CAAC,EAAE,OAAO,CAAC,EAAE;oBAC5B,CAAC;;YACD,2BAA2B;YAC3B,iBAAiB,UAAU,OAAO;QACpC;oDACA;QAAC;KAAiB;IAGpB,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;wCAAO;oBACL,iBAAiB,MAAM;gBACzB;;QACF;+BAAG;QAAC;KAAiB;IAErB,gBAAgB;IAChB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YAChC,EAAE,cAAc;YAChB,EAAE,eAAe;YAEjB;wDAAQ,CAAC,WAAa;2BACjB;wBACH;4BACE,IAAI,CAAC,CAAC,SAAS,MAAM,GAAG,CAAC;4BACzB,UAAU,CAAC,SAAS,MAAM,GAAG,CAAC,EAAE,QAAQ;wBAC1C;qBACD;;QACH;+CAAG,EAAE;IAEL,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAChC,CAAC;YACC,IAAI,KAAK,MAAM,IAAI,GAAG;YAEtB,MAAM,cAAc,IAAI,CAAC,SAAS;YAElC,wFAAwF;YACxF,+DAA+D;YAC/D,IAAI,YAAY,EAAE,GAAG,GAAG;gBACtB,QAAQ,GAAG,CACT,CAAC,kBAAkB,EAAE,YAAY,EAAE,CAAC,uCAAuC,CAAC;gBAE9E;YACF;YAEA;2DAAQ,CAAC,WACP,SACG,MAAM;mEAAC,CAAC,GAAG,QAAU,UAAU;kEAC/B,GAAG;mEAAC,CAAC,KAAK,MAAQ,CAAC;gCAClB,GAAG,GAAG;gCACN,UAAU,CAAC,MAAM,CAAC,EAAE,QAAQ;4BAC9B,CAAC;;;YAGL;2DAAmB,CAAC;oBAClB,MAAM,YAAY;wBAAE,GAAG,UAAU;oBAAC;oBAClC,OAAO,IAAI,CAAC,WAAW,OAAO;mEAAC,CAAC;4BAC9B,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI,KAAK,CAAC;4BAC7B,IAAI,UAAU,YAAY,EAAE,CAAC,QAAQ,IAAI;gCACvC,OAAO,SAAS,CAAC,IAAI;4BACvB;wBACF;;oBAEA,6CAA6C;oBAC7C,MAAM,YAAY;wBAAE,GAAG,UAAU;wBAAE,GAAG,SAAS;oBAAC;oBAChD,MAAM,oBAAiC,OAAO,OAAO,CAAC,WACnD,MAAM;qFAAC,CAAC,GAAG,MAAM,GAAK,MAAM,IAAI;oFAChC,GAAG;qFAAC,CAAC,CAAC,KAAK,MAAM;4BAChB,MAAM,CAAC,OAAO,MAAM,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC;4BAC1C,OAAO;gCAAE,UAAU;gCAAO,QAAQ;gCAAO;4BAAM;wBACjD;;oBAEF,SAAS;oBACT,OAAO;gBACT;;QACF;kDACA;QAAC;QAAM;QAAU;KAAW;IAG9B,gEAAgE;IAChE,qFAAqF;IACrF,gDAAgD;IAChD,kFAAkF;IAClF,gEAAgE;IAChE,mBAAmB;IACnB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yCAAE;YACxB,IAAI,CAAC,KAAK,MAAM,EAAE;gBAChB,qBACE,6LAAC,6HAAA,CAAA,WAAU;;sCACT,6LAAC,6HAAA,CAAA,YAAS;4BAAC,WAAU;sCAAmD;;;;;;wBAGvE,eAAe,aAAa,CAAC,GAAG;6DAAC,CAAC;gCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;gCAClD,IAAI,CAAC,aAAa,MAAM,EAAE;oCACxB,MAAM,YAAY,UAAU,CAAC,GAAG,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;oCAC1D,MAAM,kBAAkB,UAAU,IAAI,OAAO;oCAE7C,qBACE,6LAAC,6HAAA,CAAA,YAAS;wCAER,WAAU;kDAET,kBACC,8DAA8D;sDAC9D,6LAAC;4CAAI,WAAU;sDACZ;;;;;mDAGH,kCAAkC;sDAClC,6LAAC,6HAAA,CAAA,QAAK;4CACJ,OAAO,eAAe,CAAC,GAAG,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;4CACpD,QAAQ;iFAAE,CAAC,IACT,kBACE,UAAU,EAAE,EACZ,UACA,EAAE,MAAM,CAAC,KAAK;;4CAGlB,aAAY;4CACZ,WAAU;;;;;;uCApBT,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,OAAO,CAAC;;;;;gCAyBxC;gCACA,OAAO,aAAa,GAAG;qEAAC,CAAC;wCACvB,MAAM,YAAY,UAAU,CAAC,GAAG,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;wCACzD,MAAM,kBAAkB,UAAU,IAAI,OAAO;wCAE7C,qBACE,6LAAC,6HAAA,CAAA,YAAS;4CAER,WAAU;sDAET,kBACC,8DAA8D;0DAC9D,6LAAC;gDAAI,WAAU;0DACZ;;;;;uDAGH,kCAAkC;0DAClC,6LAAC,6HAAA,CAAA,QAAK;gDACJ,OAAO,eAAe,CAAC,GAAG,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;gDACnD,QAAQ;qFAAE,CAAC,IACT,kBACE,SAAS,EAAE,EACX,UACA,EAAE,MAAM,CAAC,KAAK;;gDAGlB,aAAY;gDACZ,WAAU;;;;;;2CApBT,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,OAAO,CAAC;;;;;oCAyBvC;;4BACF;;sCACA,6LAAC,6HAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;YAG3B;YAEA,OAAO,KAAK,GAAG;iDAAC,CAAC,KAAK,yBACpB,6LAAC,6HAAA,CAAA,WAAU;wBAET,WAAW,WAAW,MAAM,IAAI,aAAa;;0CAE7C,6LAAC,6HAAA,CAAA,YAAS;gCAAC,WAAU;0CAClB,WAAW;;;;;;4BAEb,eAAe,aAAa,CAAC,GAAG;iEAAC,CAAC;oCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;oCAClD,IAAI,CAAC,aAAa,MAAM,EAAE;wCACxB,mDAAmD;wCACnD,MAAM,UAAU,GAAG,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;wCAC3C,MAAM,YAAY,UAAU,CAAC,QAAQ,IAAI;wCACzC,MAAM,kBAAkB,UAAU,IAAI,OAAO;wCAE7C,qBACE,6LAAC,6HAAA,CAAA,YAAS;4CAER,WAAU;sDAET,kBACC,8DAA8D;0DAC9D,6LAAC;gDAAI,WAAU;0DACZ;;;;;uDAGH,kCAAkC;0DAClC,6LAAC,6HAAA,CAAA,QAAK;gDACJ,OAAO,eAAe,CAAC,GAAG,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI;gDACvD,QAAQ;qFAAE,CAAC,IACT,kBAAkB,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;;gDAExD,aAAY;gDACZ,WAAU;;;;;;2CAhBT,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;;;;;oCAqB3C;oCACA,OAAO,aAAa,GAAG;yEAAC,CAAC;4CACvB,MAAM,UAAU,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;4CAC1C,MAAM,YAAY,UAAU,CAAC,QAAQ,IAAI;4CACzC,MAAM,kBAAkB,UAAU,IAAI,OAAO;4CAE7C,qBACE,6LAAC,6HAAA,CAAA,YAAS;gDAER,WAAU;0DAET,kBACC,8DAA8D;8DAC9D,6LAAC;oDAAI,WAAU;8DACZ;;;;;2DAGH,kCAAkC;8DAClC,6LAAC,6HAAA,CAAA,QAAK;oDACJ,OAAO,eAAe,CAAC,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI;oDACtD,QAAQ;yFAAE,CAAC,IACT,kBAAkB,SAAS,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;;oDAEvD,aAAY;oDACZ,WAAU;;;;;;+CAhBT,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;;;;;wCAqB1C;;gCACF;;0CACA,6LAAC,6HAAA,CAAA,YAAS;gCAAC,WAAU;0CACnB,cAAA,6LAAC;oCACC,MAAK;oCACL,WAAW,GACT,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE,GAAG,IACzB,qCACA,mCACJ;oCACF,OAAO;yEAAE,IAAM,gBAAgB;;oCAC/B,UAAU,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE,GAAG;oCACvC,cACE,IAAI,EAAE,GAAG,IAAI,qCAAqC;oCAEpD,OACE,IAAI,EAAE,GAAG,IACL,8DACA;8CAGN,cAAA,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;uBAxFjB,IAAI,EAAE;;;;;;QA6FjB;wCAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe,CAAC,QAAQ,MAAM;IAEpC,qBACE,6LAAC;QAAI,WAAU;kBACZ,wBACC,6LAAC;YAAI,WAAU;sBAAmB;;;;;mBAChC,sBACF,6LAAC;YAAI,WAAU;sBAAiC;;;;;mBAC9C,6BACF,6LAAC;YAAI,WAAU;sBAAkC;;;;;iCAKjD;;8BACE,6LAAC,6HAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,6LAAC,6HAAA,CAAA,cAAW;;8CACV,6LAAC,6HAAA,CAAA,WAAU;;sDACT,6LAAC,6HAAA,CAAA,YAAS;4CACR,WAAU;4CACV,SAAS,eAAe,eAAe,GAAG,IAAI;sDAC/C;;;;;;wCAGA,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;4CACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;4CAClD,MAAM,UAAU,aAAa,MAAM,IAAI;4CACvC,qBACE,6LAAC,6HAAA,CAAA,YAAS;gDAER,SAAS;gDACT,WAAU;gDACV,SAAS,aAAa,MAAM,KAAK,IAAI,IAAI;0DAExC,UAAU,UAAU;+CALhB,UAAU,EAAE;;;;;wCAQvB;;;;;;;gCAED,eAAe,eAAe,kBAC7B,6LAAC,6HAAA,CAAA,WAAU;8CACR,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;wCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;wCAClD,IAAI,CAAC,aAAa,MAAM,EAAE,OAAO;wCACjC,OAAO,aAAa,GAAG,CAAC,CAAC,yBACvB,6LAAC,6HAAA,CAAA,YAAS;gDAER,WAAU;0DAET,SAAS,UAAU;+CAHf,SAAS,EAAE;;;;;oCAMtB;;;;;;;;;;;;sCAIN,6LAAC,6HAAA,CAAA,YAAS;sCAAE;;;;;;;;;;;;8BAEd,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;;;;;;;AAO1C;;QA1hBM,8KAAA,CAAA,WAAQ;;;;QAAR,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 2077, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/app/form-test/%5BhashedId%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from \"react\";\r\nimport { Question, QuestionGroup } from \"@/types/formBuilder\";\r\nimport { useQuery, useMutation } from \"@tanstack/react-query\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { decode } from \"@/lib/encodeDecode\";\r\nimport { fetchQuestions } from \"@/lib/api/form-builder\";\r\nimport { fetchQuestionGroups } from \"@/lib/api/question-groups\";\r\nimport { createAnswerSubmission, fetchProjectById } from \"@/lib/api/projects\";\r\nimport { Project } from \"@/types\";\r\nimport Spinner from \"@/components/general/Spinner\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\r\nimport { ChevronDown, ChevronRight } from \"lucide-react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { TableInput } from \"@/components/form-inputs/TableInput\";\r\nimport debounce from \"lodash/debounce\";\r\n\r\nexport default function FormTestPage() {\r\n  const dispatch = useDispatch();\r\n  const { hashedId } = useParams();\r\n  const hashedIdString = hashedId as string;\r\n  const projectId = decode(hashedIdString);\r\n\r\n  const [answers, setAnswers] = useState<Record<string, any>>({});\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [expandedGroups, setExpandedGroups] = useState<Record<number, boolean>>(\r\n    {}\r\n  );\r\n\r\n  const {\r\n    data: questionsData,\r\n    isLoading,\r\n    isError,\r\n  } = useQuery<Question[]>({\r\n    queryKey: [\"questions\", projectId],\r\n    queryFn: () => fetchQuestions({ projectId: projectId! }),\r\n    enabled: !!projectId,\r\n  });\r\n\r\n  const { data: questionGroups = [] } = useQuery<QuestionGroup[]>({\r\n    queryKey: [\"questionGroups\", projectId],\r\n    queryFn: () => fetchQuestionGroups({ projectId: projectId! }),\r\n    enabled: !!projectId,\r\n  });\r\n\r\n  const { data: projectData } = useQuery<Project>({\r\n    queryKey: [\"project\", projectId],\r\n    queryFn: () => fetchProjectById({ projectId: projectId! }),\r\n    enabled: !!projectId,\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (questionsData) {\r\n      const initialAnswers: Record<string, any> = {};\r\n      questionsData.forEach((question) => {\r\n        initialAnswers[question.id] =\r\n          question.inputType === \"selectmany\" ? [] : \"\";\r\n      });\r\n      setAnswers(initialAnswers);\r\n    }\r\n  }, [questionsData]);\r\n\r\n  useEffect(() => {\r\n    if (questionGroups.length > 0) {\r\n      const initialExpandedState: Record<number, boolean> = {};\r\n      questionGroups.forEach((group) => {\r\n        initialExpandedState[group.id] = true;\r\n      });\r\n      setExpandedGroups(initialExpandedState);\r\n    }\r\n  }, [questionGroups.length]);\r\n\r\n  const groupedQuestions = useMemo(() => {\r\n    return questionGroups.reduce(\r\n      (acc: Record<number, Question[]>, group: QuestionGroup) => {\r\n        acc[group.id] =\r\n          questionsData?.filter((q) => q.questionGroupId === group.id) || [];\r\n        return acc;\r\n      },\r\n      {} as Record<number, Question[]>\r\n    );\r\n  }, [questionGroups, questionsData]);\r\n\r\n  const ungroupedQuestions = useMemo(() => {\r\n    return (\r\n      questionsData?.filter(\r\n        (q) => q.questionGroupId === null || q.questionGroupId === undefined\r\n      ) || []\r\n    );\r\n  }, [questionsData]);\r\n\r\n  const unifiedFormItems = useMemo(() => {\r\n    const items: Array<{\r\n      type: \"group\" | \"question\";\r\n      data: QuestionGroup | Question;\r\n      order: number;\r\n      originalPosition?: number;\r\n    }> = [];\r\n\r\n    questionGroups.forEach((group: QuestionGroup) => {\r\n      const groupQuestions =\r\n        questionsData?.filter((q) => q.questionGroupId === group.id) || [];\r\n      const minQuestionPosition =\r\n        groupQuestions.length > 0\r\n          ? Math.min(...groupQuestions.map((q) => q.position))\r\n          : group.order;\r\n\r\n      items.push({\r\n        type: \"group\",\r\n        data: group,\r\n        order: minQuestionPosition,\r\n        originalPosition: minQuestionPosition,\r\n      });\r\n    });\r\n\r\n    ungroupedQuestions.forEach((question: Question) => {\r\n      items.push({\r\n        type: \"question\",\r\n        data: question,\r\n        order: question.position,\r\n        originalPosition: question.position,\r\n      });\r\n    });\r\n\r\n    return items.sort((a, b) => {\r\n      if (a.order === b.order) {\r\n        return (\r\n          (a.originalPosition || a.order) - (b.originalPosition || b.order)\r\n        );\r\n      }\r\n      return a.order - b.order;\r\n    });\r\n  }, [questionGroups, ungroupedQuestions, questionsData]);\r\n\r\n  const toggleGroupExpansion = useCallback((groupId: number) => {\r\n    setExpandedGroups((prev) => ({\r\n      ...prev,\r\n      [groupId]: !prev[groupId],\r\n    }));\r\n  }, []);\r\n\r\n  const submitAnswersMutation = useMutation({\r\n    mutationFn: async (answers: Record<string, any>) => {\r\n      const formattedAnswers =\r\n        questionsData?.map((question) => {\r\n          const answerValue = answers[question.id];\r\n          const isSelectMany = question.inputType === \"selectmany\";\r\n\r\n          let questionOptionId: number | number[] | undefined;\r\n\r\n          if (\r\n            isSelectMany &&\r\n            Array.isArray(answerValue) &&\r\n            question.questionOptions\r\n          ) {\r\n            questionOptionId = answerValue\r\n              .map((val: string) => {\r\n                const option = question.questionOptions.find(\r\n                  (opt) => opt.label === val\r\n                );\r\n                return option?.id;\r\n              })\r\n              .filter((id: number | undefined) => id !== undefined) as number[];\r\n          } else if (\r\n            question.inputType === \"selectone\" &&\r\n            answerValue &&\r\n            question.questionOptions\r\n          ) {\r\n            const option = question.questionOptions.find(\r\n              (opt) => opt.label === answerValue\r\n            );\r\n            questionOptionId = option?.id;\r\n          }\r\n\r\n          let formattedValue: string | number | boolean | undefined;\r\n          if (isSelectMany) {\r\n            formattedValue =\r\n              Array.isArray(answerValue) && answerValue.length > 0\r\n                ? answerValue.join(\", \")\r\n                : undefined;\r\n          } else if (question.inputType === \"number\") {\r\n            formattedValue = answerValue ? Number(answerValue) : undefined;\r\n          } else if (\r\n            question.inputType === \"date\" ||\r\n            question.inputType === \"dateandtime\"\r\n          ) {\r\n            formattedValue = answerValue || undefined;\r\n          } else if (question.inputType === \"table\") {\r\n            if (Array.isArray(answerValue) && answerValue.length > 0) {\r\n              try {\r\n                const validatedCellValues = answerValue.map((cell) => ({\r\n                  columnId: Number(cell.columnId),\r\n                  rowsId: Number(cell.rowsId),\r\n                  value: String(cell.value || \"\"),\r\n                }));\r\n                formattedValue = JSON.stringify(validatedCellValues);\r\n              } catch (err) {\r\n                console.error(\"Error formatting table data:\", err);\r\n                formattedValue = undefined;\r\n              }\r\n            } else {\r\n              formattedValue = undefined;\r\n            }\r\n          } else {\r\n            formattedValue = answerValue ? String(answerValue) : undefined;\r\n          }\r\n\r\n          return {\r\n            projectId: Number(projectId),\r\n            questionId: question.id,\r\n            answerType: String(question.inputType),\r\n            value: formattedValue,\r\n            questionOptionId,\r\n            isOtherOption: false,\r\n          };\r\n        }) || [];\r\n\r\n      return await createAnswerSubmission(formattedAnswers);\r\n    },\r\n    onSuccess: (data) => {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Form submitted successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n      setAnswers({});\r\n      window.dispatchEvent(new Event(\"form-submitted\"));\r\n      localStorage.setItem(\"form_submitted\", Date.now().toString());\r\n    },\r\n    onError: (error: any) => {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to submit form. Please try again.\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      console.error(\"Submission Error:\", error);\r\n    },\r\n    onSettled: () => {\r\n      setIsSubmitting(false);\r\n    },\r\n  });\r\n\r\n  const handleTableInputChange = useCallback(\r\n    debounce(\r\n      (questionId: number, cellValues: any) => {\r\n        setAnswers((prev) => ({\r\n          ...prev,\r\n          [questionId]: cellValues,\r\n        }));\r\n        setErrors((prev) => ({\r\n          ...prev,\r\n          [questionId]: \"\",\r\n        }));\r\n      },\r\n      500,\r\n      { leading: false, trailing: true }\r\n    ),\r\n    []\r\n  );\r\n\r\n  const handleInputChange = useCallback(\r\n    (questionId: number, value: any) => {\r\n      if (\r\n        questionsData?.find((q) => q.id === questionId)?.inputType === \"table\"\r\n      ) {\r\n        handleTableInputChange(questionId, value);\r\n      } else {\r\n        setAnswers((prev) => ({\r\n          ...prev,\r\n          [questionId]: value,\r\n        }));\r\n        setErrors((prev) => ({\r\n          ...prev,\r\n          [questionId]: \"\",\r\n        }));\r\n      }\r\n    },\r\n    [questionsData, handleTableInputChange]\r\n  );\r\n\r\n  const validateForm = () => {\r\n    const newErrors: Record<string, string> = {};\r\n    let isValid = true;\r\n\r\n    questionsData?.forEach((question) => {\r\n      if (question.isRequired) {\r\n        const value = answers[question.id];\r\n        if (\r\n          (typeof value === \"string\" && !value.trim()) ||\r\n          (Array.isArray(value) && value.length === 0) ||\r\n          value === undefined ||\r\n          value === null\r\n        ) {\r\n          newErrors[question.id] = `${question.label} is required`;\r\n          isValid = false;\r\n        }\r\n      }\r\n    });\r\n\r\n    setErrors(newErrors);\r\n    return isValid;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!validateForm()) return;\r\n    setIsSubmitting(true);\r\n    submitAnswersMutation.mutate(answers);\r\n  };\r\n\r\n  const renderQuestionInput = (question: Question) => {\r\n    const value =\r\n      answers[question.id] ?? (question.inputType === \"selectmany\" ? [] : \"\");\r\n\r\n    switch (question.inputType) {\r\n      case \"text\":\r\n        if (question.hint?.includes(\"multiline\")) {\r\n          return (\r\n            <Textarea\r\n              value={value}\r\n              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>\r\n                handleInputChange(question.id, e.target.value)\r\n              }\r\n              placeholder={question.placeholder || \"Your answer\"}\r\n              required={question.isRequired}\r\n            />\r\n          );\r\n        }\r\n        return (\r\n          <input\r\n            className=\"input-field w-full\"\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.placeholder || \"Your answer\"}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"number\":\r\n        return (\r\n          <input\r\n            className=\"input-field w-full\"\r\n            type=\"number\"\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.placeholder || \"Your answer\"}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"decimal\":\r\n        return (\r\n          <input\r\n            className=\"input-field w-full\"\r\n            type=\"decimal\"\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.placeholder || \"Your answer\"}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"selectone\":\r\n        return (\r\n          <RadioGroup\r\n            value={value}\r\n            onValueChange={(val: string) => handleInputChange(question.id, val)}\r\n            required={question.isRequired}\r\n          >\r\n            <div className=\"space-y-2\">\r\n              {question.questionOptions?.map((option, index) => (\r\n                <div key={index} className=\"flex items-center space-x-2\">\r\n                  <RadioGroupItem\r\n                    value={option.label}\r\n                    id={`option-${option.id}`}\r\n                  />\r\n                  <Label\r\n                    htmlFor={`option-${option.id}`}\r\n                    className=\"cursor-pointer\"\r\n                  >\r\n                    {option.label}\r\n                  </Label>\r\n                  {option.sublabel && (\r\n                    <p className=\"text-sm text-neutral-700 ml-4\">\r\n                      {`(${option.sublabel})`}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </RadioGroup>\r\n        );\r\n\r\n      case \"selectmany\":\r\n        return (\r\n          <div className=\"space-y-2\">\r\n            {question.questionOptions?.map((option) => (\r\n              <div key={option.id} className=\"flex items-center space-x-2\">\r\n                <Checkbox\r\n                  id={`option-${option.id}`}\r\n                  checked={(value || []).includes(option.label)}\r\n                  onCheckedChange={(checked) => {\r\n                    const currentValues = value || [];\r\n                    const newValues = checked\r\n                      ? [...currentValues, option.label]\r\n                      : currentValues.filter((v: string) => v !== option.label);\r\n                    handleInputChange(question.id, newValues);\r\n                  }}\r\n                />\r\n                <Label\r\n                  htmlFor={`option-${option.id}`}\r\n                  className=\"cursor-pointer\"\r\n                >\r\n                  {option.label}\r\n                </Label>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        );\r\n\r\n      case \"date\":\r\n        return (\r\n          <div className=\"relative\">\r\n            <input\r\n              className=\"input-field w-full\"\r\n              type=\"date\"\r\n              value={value}\r\n              onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n              placeholder={question.placeholder || \"Select date\"}\r\n              required={question.isRequired}\r\n            />\r\n          </div>\r\n        );\r\n\r\n      case \"dateandtime\":\r\n        return (\r\n          <div className=\"relative\">\r\n            <input\r\n              className=\"input-field w-full\"\r\n              type=\"time\"\r\n              value={value}\r\n              onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n              placeholder={question.placeholder || \"Select time\"}\r\n              required={question.isRequired}\r\n            />\r\n          </div>\r\n        );\r\n\r\n      case \"table\":\r\n        return (\r\n          <TableInput\r\n            questionId={question.id}\r\n            value={value}\r\n            onChange={(cellValues) =>\r\n              handleInputChange(question.id, cellValues)\r\n            }\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const renderQuestion = (question: Question) => (\r\n    <div\r\n      key={question.id}\r\n      className=\"border border-gray-200 dark:bg-gray-800 rounded-md p-4\"\r\n    >\r\n      <div className=\"mb-2\">\r\n        <Label className=\"text-base font-medium\">\r\n          {question.label}\r\n          {question.isRequired && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </Label>\r\n        {question.hint && (\r\n          <p className=\"text-sm text-muted-foreground mt-1\">{question.hint}</p>\r\n        )}\r\n        {errors[question.id] && (\r\n          <p className=\"text-sm text-red-500 mt-1\">{errors[question.id]}</p>\r\n        )}\r\n      </div>\r\n      <div className=\"mt-2\">{renderQuestionInput(question)}</div>\r\n    </div>\r\n  );\r\n\r\n  if (isLoading) return <Spinner />;\r\n  if (isError || !questionsData) {\r\n    return (\r\n      <p className=\"text-sm text-red-500\">\r\n        Error loading form. Please try again.\r\n      </p>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6\">\r\n      <div className=\"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700\">\r\n        <h2 className=\"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          {projectData?.name || \"Test Form\"}\r\n        </h2>\r\n        <form onSubmit={handleSubmit} className=\"p-6\">\r\n          <div className=\"space-y-6\">\r\n            {!questionsData || questionsData.length === 0 ? (\r\n              <div className=\"text-center py-12\">\r\n                <p className=\"text-muted-foreground\">\r\n                  This form has no questions yet.\r\n                </p>\r\n              </div>\r\n            ) : (\r\n              unifiedFormItems.map((item) => {\r\n                if (item.type === \"group\") {\r\n                  const group = item.data as QuestionGroup;\r\n                  const groupQuestions = groupedQuestions[group.id] || [];\r\n                  const isExpanded = expandedGroups[group.id];\r\n\r\n                  return (\r\n                    <div\r\n                      key={`group-${group.id}`}\r\n                      className=\"border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800\"\r\n                    >\r\n                      <div\r\n                        className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\"\r\n                        onClick={() => toggleGroupExpansion(group.id)}\r\n                      >\r\n                        <div className=\"flex items-center space-x-2\">\r\n                          {isExpanded ? (\r\n                            <ChevronDown className=\"h-5 w-5 text-gray-500\" />\r\n                          ) : (\r\n                            <ChevronRight className=\"h-5 w-5 text-gray-500\" />\r\n                          )}\r\n                          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                            {group.title}\r\n                          </h3>\r\n                          <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                            ({groupQuestions.length} question\r\n                            {groupQuestions.length !== 1 ? \"s\" : \"\"})\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                      {isExpanded && (\r\n                        <div className=\"p-4 space-y-4\">\r\n                          {groupQuestions.length > 0 ? (\r\n                            groupQuestions.map((question) =>\r\n                              renderQuestion(question)\r\n                            )\r\n                          ) : (\r\n                            <div className=\"text-center py-4 text-gray-500 dark:text-gray-400\">\r\n                              No questions in this group.\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  );\r\n                } else {\r\n                  const question = item.data as Question;\r\n                  return renderQuestion(question);\r\n                }\r\n              })\r\n            )}\r\n            {questionsData && questionsData.length > 0 && (\r\n              <div className=\"mt-6 flex justify-end\">\r\n                <button\r\n                  className=\"btn-primary\"\r\n                  type=\"submit\"\r\n                  disabled={isSubmitting}\r\n                >\r\n                  {isSubmitting ? \"Submitting...\" : \"Submit Form\"}\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AApBA;;;;;;;;;;;;;;;;;;AAsBe,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,iBAAiB;IACvB,MAAM,YAAY,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACjD,CAAC;IAGH,MAAM,EACJ,MAAM,aAAa,EACnB,SAAS,EACT,OAAO,EACR,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAc;QACvB,UAAU;YAAC;YAAa;SAAU;QAClC,OAAO;qCAAE,IAAM,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;oBAAE,WAAW;gBAAW;;QACtD,SAAS,CAAC,CAAC;IACb;IAEA,MAAM,EAAE,MAAM,iBAAiB,EAAE,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAmB;QAC9D,UAAU;YAAC;YAAkB;SAAU;QACvC,OAAO;qCAAE,IAAM,CAAA,GAAA,mIAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAE,WAAW;gBAAW;;QAC3D,SAAS,CAAC,CAAC;IACb;IAEA,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAW;QAC9C,UAAU;YAAC;YAAW;SAAU;QAChC,OAAO;qCAAE,IAAM,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD,EAAE;oBAAE,WAAW;gBAAW;;QACxD,SAAS,CAAC,CAAC;IACb;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,eAAe;gBACjB,MAAM,iBAAsC,CAAC;gBAC7C,cAAc,OAAO;8CAAC,CAAC;wBACrB,cAAc,CAAC,SAAS,EAAE,CAAC,GACzB,SAAS,SAAS,KAAK,eAAe,EAAE,GAAG;oBAC/C;;gBACA,WAAW;YACb;QACF;iCAAG;QAAC;KAAc;IAElB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,MAAM,uBAAgD,CAAC;gBACvD,eAAe,OAAO;8CAAC,CAAC;wBACtB,oBAAoB,CAAC,MAAM,EAAE,CAAC,GAAG;oBACnC;;gBACA,kBAAkB;YACpB;QACF;iCAAG;QAAC,eAAe,MAAM;KAAC;IAE1B,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YAC/B,OAAO,eAAe,MAAM;0DAC1B,CAAC,KAAiC;oBAChC,GAAG,CAAC,MAAM,EAAE,CAAC,GACX,eAAe;kEAAO,CAAC,IAAM,EAAE,eAAe,KAAK,MAAM,EAAE;oEAAK,EAAE;oBACpE,OAAO;gBACT;yDACA,CAAC;QAEL;iDAAG;QAAC;QAAgB;KAAc;IAElC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDAAE;YACjC,OACE,eAAe;4DACb,CAAC,IAAM,EAAE,eAAe,KAAK,QAAQ,EAAE,eAAe,KAAK;8DACxD,EAAE;QAEX;mDAAG;QAAC;KAAc;IAElB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YAC/B,MAAM,QAKD,EAAE;YAEP,eAAe,OAAO;0DAAC,CAAC;oBACtB,MAAM,iBACJ,eAAe;kEAAO,CAAC,IAAM,EAAE,eAAe,KAAK,MAAM,EAAE;oEAAK,EAAE;oBACpE,MAAM,sBACJ,eAAe,MAAM,GAAG,IACpB,KAAK,GAAG,IAAI,eAAe,GAAG;kEAAC,CAAC,IAAM,EAAE,QAAQ;oEAChD,MAAM,KAAK;oBAEjB,MAAM,IAAI,CAAC;wBACT,MAAM;wBACN,MAAM;wBACN,OAAO;wBACP,kBAAkB;oBACpB;gBACF;;YAEA,mBAAmB,OAAO;0DAAC,CAAC;oBAC1B,MAAM,IAAI,CAAC;wBACT,MAAM;wBACN,MAAM;wBACN,OAAO,SAAS,QAAQ;wBACxB,kBAAkB,SAAS,QAAQ;oBACrC;gBACF;;YAEA,OAAO,MAAM,IAAI;0DAAC,CAAC,GAAG;oBACpB,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE;wBACvB,OACE,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK;oBAEpE;oBACA,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;gBAC1B;;QACF;iDAAG;QAAC;QAAgB;QAAoB;KAAc;IAEtD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACxC;kEAAkB,CAAC,OAAS,CAAC;wBAC3B,GAAG,IAAI;wBACP,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;oBAC3B,CAAC;;QACH;yDAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACxC,UAAU;+DAAE,OAAO;gBACjB,MAAM,mBACJ,eAAe;uEAAI,CAAC;wBAClB,MAAM,cAAc,OAAO,CAAC,SAAS,EAAE,CAAC;wBACxC,MAAM,eAAe,SAAS,SAAS,KAAK;wBAE5C,IAAI;wBAEJ,IACE,gBACA,MAAM,OAAO,CAAC,gBACd,SAAS,eAAe,EACxB;4BACA,mBAAmB,YAChB,GAAG;mFAAC,CAAC;oCACJ,MAAM,SAAS,SAAS,eAAe,CAAC,IAAI;kGAC1C,CAAC,MAAQ,IAAI,KAAK,KAAK;;oCAEzB,OAAO,QAAQ;gCACjB;kFACC,MAAM;mFAAC,CAAC,KAA2B,OAAO;;wBAC/C,OAAO,IACL,SAAS,SAAS,KAAK,eACvB,eACA,SAAS,eAAe,EACxB;4BACA,MAAM,SAAS,SAAS,eAAe,CAAC,IAAI;0FAC1C,CAAC,MAAQ,IAAI,KAAK,KAAK;;4BAEzB,mBAAmB,QAAQ;wBAC7B;wBAEA,IAAI;wBACJ,IAAI,cAAc;4BAChB,iBACE,MAAM,OAAO,CAAC,gBAAgB,YAAY,MAAM,GAAG,IAC/C,YAAY,IAAI,CAAC,QACjB;wBACR,OAAO,IAAI,SAAS,SAAS,KAAK,UAAU;4BAC1C,iBAAiB,cAAc,OAAO,eAAe;wBACvD,OAAO,IACL,SAAS,SAAS,KAAK,UACvB,SAAS,SAAS,KAAK,eACvB;4BACA,iBAAiB,eAAe;wBAClC,OAAO,IAAI,SAAS,SAAS,KAAK,SAAS;4BACzC,IAAI,MAAM,OAAO,CAAC,gBAAgB,YAAY,MAAM,GAAG,GAAG;gCACxD,IAAI;oCACF,MAAM,sBAAsB,YAAY,GAAG;+GAAC,CAAC,OAAS,CAAC;gDACrD,UAAU,OAAO,KAAK,QAAQ;gDAC9B,QAAQ,OAAO,KAAK,MAAM;gDAC1B,OAAO,OAAO,KAAK,KAAK,IAAI;4CAC9B,CAAC;;oCACD,iBAAiB,KAAK,SAAS,CAAC;gCAClC,EAAE,OAAO,KAAK;oCACZ,QAAQ,KAAK,CAAC,gCAAgC;oCAC9C,iBAAiB;gCACnB;4BACF,OAAO;gCACL,iBAAiB;4BACnB;wBACF,OAAO;4BACL,iBAAiB,cAAc,OAAO,eAAe;wBACvD;wBAEA,OAAO;4BACL,WAAW,OAAO;4BAClB,YAAY,SAAS,EAAE;4BACvB,YAAY,OAAO,SAAS,SAAS;4BACrC,OAAO;4BACP;4BACA,eAAe;wBACjB;oBACF;yEAAM,EAAE;gBAEV,OAAO,MAAM,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD,EAAE;YACtC;;QACA,SAAS;+DAAE,CAAC;gBACV,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,WAAW,CAAC;gBACZ,OAAO,aAAa,CAAC,IAAI,MAAM;gBAC/B,aAAa,OAAO,CAAC,kBAAkB,KAAK,GAAG,GAAG,QAAQ;YAC5D;;QACA,OAAO;+DAAE,CAAC;gBACR,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,QAAQ,KAAK,CAAC,qBAAqB;YACrC;;QACA,SAAS;+DAAE;gBACT,gBAAgB;YAClB;;IACF;IAEA,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EACvC,CAAA,GAAA,qIAAA,CAAA,UAAQ,AAAD;4DACL,CAAC,YAAoB;YACnB;oEAAW,CAAC,OAAS,CAAC;wBACpB,GAAG,IAAI;wBACP,CAAC,WAAW,EAAE;oBAChB,CAAC;;YACD;oEAAU,CAAC,OAAS,CAAC;wBACnB,GAAG,IAAI;wBACP,CAAC,WAAW,EAAE;oBAChB,CAAC;;QACH;2DACA,KACA;QAAE,SAAS;QAAO,UAAU;IAAK,IAEnC,EAAE;IAGJ,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAClC,CAAC,YAAoB;YACnB,IACE,eAAe;+DAAK,CAAC,IAAM,EAAE,EAAE,KAAK;+DAAa,cAAc,SAC/D;gBACA,uBAAuB,YAAY;YACrC,OAAO;gBACL;mEAAW,CAAC,OAAS,CAAC;4BACpB,GAAG,IAAI;4BACP,CAAC,WAAW,EAAE;wBAChB,CAAC;;gBACD;mEAAU,CAAC,OAAS,CAAC;4BACnB,GAAG,IAAI;4BACP,CAAC,WAAW,EAAE;wBAChB,CAAC;;YACH;QACF;sDACA;QAAC;QAAe;KAAuB;IAGzC,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAC3C,IAAI,UAAU;QAEd,eAAe,QAAQ,CAAC;YACtB,IAAI,SAAS,UAAU,EAAE;gBACvB,MAAM,QAAQ,OAAO,CAAC,SAAS,EAAE,CAAC;gBAClC,IACE,AAAC,OAAO,UAAU,YAAY,CAAC,MAAM,IAAI,MACxC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,KAC1C,UAAU,aACV,UAAU,MACV;oBACA,SAAS,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,SAAS,KAAK,CAAC,YAAY,CAAC;oBACxD,UAAU;gBACZ;YACF;QACF;QAEA,UAAU;QACV,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,gBAAgB;QACrB,gBAAgB;QAChB,sBAAsB,MAAM,CAAC;IAC/B;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,QACJ,OAAO,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS,SAAS,KAAK,eAAe,EAAE,GAAG,EAAE;QAExE,OAAQ,SAAS,SAAS;YACxB,KAAK;gBACH,IAAI,SAAS,IAAI,EAAE,SAAS,cAAc;oBACxC,qBACE,6LAAC,gIAAA,CAAA,WAAQ;wBACP,OAAO;wBACP,UAAU,CAAC,IACT,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wBAE/C,aAAa,SAAS,WAAW,IAAI;wBACrC,UAAU,SAAS,UAAU;;;;;;gBAGnC;gBACA,qBACE,6LAAC;oBACC,WAAU;oBACV,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,WAAW,IAAI;oBACrC,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,6LAAC;oBACC,WAAU;oBACV,MAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,WAAW,IAAI;oBACrC,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,6LAAC;oBACC,WAAU;oBACV,MAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,WAAW,IAAI;oBACrC,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,6LAAC,sIAAA,CAAA,aAAU;oBACT,OAAO;oBACP,eAAe,CAAC,MAAgB,kBAAkB,SAAS,EAAE,EAAE;oBAC/D,UAAU,SAAS,UAAU;8BAE7B,cAAA,6LAAC;wBAAI,WAAU;kCACZ,SAAS,eAAe,EAAE,IAAI,CAAC,QAAQ,sBACtC,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC,sIAAA,CAAA,iBAAc;wCACb,OAAO,OAAO,KAAK;wCACnB,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;;;;;;kDAE3B,6LAAC,6HAAA,CAAA,QAAK;wCACJ,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;wCAC9B,WAAU;kDAET,OAAO,KAAK;;;;;;oCAEd,OAAO,QAAQ,kBACd,6LAAC;wCAAE,WAAU;kDACV,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC;;;;;;;+BAbnB;;;;;;;;;;;;;;;YAsBpB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACZ,SAAS,eAAe,EAAE,IAAI,CAAC,uBAC9B,6LAAC;4BAAoB,WAAU;;8CAC7B,6LAAC,gIAAA,CAAA,WAAQ;oCACP,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oCACzB,SAAS,CAAC,SAAS,EAAE,EAAE,QAAQ,CAAC,OAAO,KAAK;oCAC5C,iBAAiB,CAAC;wCAChB,MAAM,gBAAgB,SAAS,EAAE;wCACjC,MAAM,YAAY,UACd;+CAAI;4CAAe,OAAO,KAAK;yCAAC,GAChC,cAAc,MAAM,CAAC,CAAC,IAAc,MAAM,OAAO,KAAK;wCAC1D,kBAAkB,SAAS,EAAE,EAAE;oCACjC;;;;;;8CAEF,6LAAC,6HAAA,CAAA,QAAK;oCACJ,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oCAC9B,WAAU;8CAET,OAAO,KAAK;;;;;;;2BAhBP,OAAO,EAAE;;;;;;;;;;YAuB3B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wBAC9D,aAAa,SAAS,WAAW,IAAI;wBACrC,UAAU,SAAS,UAAU;;;;;;;;;;;YAKrC,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wBAC9D,aAAa,SAAS,WAAW,IAAI;wBACrC,UAAU,SAAS,UAAU;;;;;;;;;;;YAKrC,KAAK;gBACH,qBACE,6LAAC,8IAAA,CAAA,aAAU;oBACT,YAAY,SAAS,EAAE;oBACvB,OAAO;oBACP,UAAU,CAAC,aACT,kBAAkB,SAAS,EAAE,EAAE;oBAEjC,UAAU,SAAS,UAAU;;;;;;YAInC;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC,yBACtB,6LAAC;YAEC,WAAU;;8BAEV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6HAAA,CAAA,QAAK;4BAAC,WAAU;;gCACd,SAAS,KAAK;gCACd,SAAS,UAAU,kBAAI,6LAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;wBAE7D,SAAS,IAAI,kBACZ,6LAAC;4BAAE,WAAU;sCAAsC,SAAS,IAAI;;;;;;wBAEjE,MAAM,CAAC,SAAS,EAAE,CAAC,kBAClB,6LAAC;4BAAE,WAAU;sCAA6B,MAAM,CAAC,SAAS,EAAE,CAAC;;;;;;;;;;;;8BAGjE,6LAAC;oBAAI,WAAU;8BAAQ,oBAAoB;;;;;;;WAftC,SAAS,EAAE;;;;;IAmBpB,IAAI,WAAW,qBAAO,6LAAC,oIAAA,CAAA,UAAO;;;;;IAC9B,IAAI,WAAW,CAAC,eAAe;QAC7B,qBACE,6LAAC;YAAE,WAAU;sBAAuB;;;;;;IAIxC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BACX,aAAa,QAAQ;;;;;;8BAExB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;8BACtC,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,CAAC,iBAAiB,cAAc,MAAM,KAAK,kBAC1C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;uCAKvC,iBAAiB,GAAG,CAAC,CAAC;gCACpB,IAAI,KAAK,IAAI,KAAK,SAAS;oCACzB,MAAM,QAAQ,KAAK,IAAI;oCACvB,MAAM,iBAAiB,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;oCACvD,MAAM,aAAa,cAAc,CAAC,MAAM,EAAE,CAAC;oCAE3C,qBACE,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDACC,WAAU;gDACV,SAAS,IAAM,qBAAqB,MAAM,EAAE;0DAE5C,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,2BACC,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEAE1B,6LAAC;4DAAG,WAAU;sEACX,MAAM,KAAK;;;;;;sEAEd,6LAAC;4DAAK,WAAU;;gEAA2C;gEACvD,eAAe,MAAM;gEAAC;gEACvB,eAAe,MAAM,KAAK,IAAI,MAAM;gEAAG;;;;;;;;;;;;;;;;;;4CAI7C,4BACC,6LAAC;gDAAI,WAAU;0DACZ,eAAe,MAAM,GAAG,IACvB,eAAe,GAAG,CAAC,CAAC,WAClB,eAAe,2BAGjB,6LAAC;oDAAI,WAAU;8DAAoD;;;;;;;;;;;;uCA7BpE,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;;;;;gCAqC9B,OAAO;oCACL,MAAM,WAAW,KAAK,IAAI;oCAC1B,OAAO,eAAe;gCACxB;4BACF;4BAED,iBAAiB,cAAc,MAAM,GAAG,mBACvC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,UAAU;8CAET,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpD;GAnjBwB;;QACL,4JAAA,CAAA,cAAW;QACP,qIAAA,CAAA,YAAS;QAe1B,8KAAA,CAAA,WAAQ;QAM0B,8KAAA,CAAA,WAAQ;QAMhB,8KAAA,CAAA,WAAQ;QAgGR,iLAAA,CAAA,cAAW;;;KA7HnB", "debugId": null}}]}